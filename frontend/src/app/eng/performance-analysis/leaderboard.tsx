import { LPLoading } from "@/components/lp-loading";
import { Suspense } from "react";
import { EngineerLeaderboard } from "./leaderboard/engineer";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { useSuspenseQuery } from "@tanstack/react-query";
import axios from "axios";
import { API_BASE_URL } from "@/constants";
import { format } from "date-fns";
import { GetEngLeaderboardResponse } from "@/types/eng/leaderboard";
import { QALeaderboard } from "./leaderboard/qa";

export function Leaderboard() {
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );

  const { data, isError } = useSuspenseQuery({
    queryKey: ["eng-leaderboard", startDate, endDate],
    queryFn: async () => {
      const res = await axios.get(API_BASE_URL + `/api/eng/leaderboard`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          start_date: startDate
            ? format(new Date(startDate), "yyyy-MM-dd")
            : undefined,
          end_date: endDate
            ? format(new Date(endDate), "yyyy-MM-dd")
            : undefined,
        },
      });
      return res.data as GetEngLeaderboardResponse;
    },
  });

  if (isError)
    return (
      <div className="flex flex-col items-center justify-center h-full animate-fade-in bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="flex items-center bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded mb-4 shadow-md animate-fade-in">
          <svg
            className="w-6 h-6 mr-3 text-red-500"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M18.364 5.636l-12.728 12.728M5.636 5.636l12.728 12.728"
            />
          </svg>
          <div>
            <span className="font-bold">Oops! Something went wrong.</span>
            <div className="text-sm mt-1">
              We couldn't load the data. Please check your connection or try
              again.
            </div>
          </div>
        </div>
        <button
          className="bg-red-500 to-yellow-400 hover:bg-red-600 text-white font-bold py-2 px-6 rounded shadow-lg focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 transition"
          onClick={() => window.location.reload()}
          aria-label="Retry loading data"
        >
          Retry
        </button>
      </div>
    );


  return (
    <div className="space-y-6">
      <Suspense
        fallback={
          <div className="flex items-center justify-center h-60">
            <LPLoading />
          </div>
        }
      >
        <EngineerLeaderboard members={data.engineers} />
      </Suspense>

      <Suspense
        fallback={
          <div className="flex items-center justify-center h-60">
            <LPLoading />
          </div>
        }
      >
        <QALeaderboard members={data.qas} />
      </Suspense>
    </div>
  );
}

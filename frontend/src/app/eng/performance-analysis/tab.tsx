"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { LPLoading } from "@/components/lp-loading";
import { Suspense } from "react";
import { Summary } from "./summary";
import { Epic } from "./epic/epic";
import { Leaderboard } from "./leaderboard";
import { Bug } from "./bug/bug";

export function Tab() {
  return (
    <div className="space-y-6">
      {/* Tabs for team views */}
      <Tabs defaultValue="summary" className="w-full">
        <TabsList className="grid w-full md:w-2/3 grid-cols-4 mb-4">
          <TabsTrigger value="summary">Summary</TabsTrigger>
          <TabsTrigger value="epics">Epics</TabsTrigger>
          <TabsTrigger value="teams">Leader Board</TabsTrigger>
          <TabsTrigger value="bugs">Bugs</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <Summary />
          </Suspense>
        </TabsContent>

        <TabsContent value="epics" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <Epic />
          </Suspense>
        </TabsContent>

        <TabsContent value="teams" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <Leaderboard />
          </Suspense>
        </TabsContent>

        <TabsContent value="bugs" className="space-y-4">
        <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <Bug />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
}

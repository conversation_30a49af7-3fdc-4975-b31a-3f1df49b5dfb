"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, ExternalLink } from "lucide-react";
import { formatDecimalHours } from "../utils/time-format";

export interface Task {
  assignee: string;
  created: string;
  key: string;
  parent_link: string;
  parent_name: string;
  priority: string;
  reporter: string;
  sp: number;
  status: string;
  task_link: string;
  task_name: string;
  time_spent_hours: number;
  time_spent_human: string;
  time_spent_second: number;
  type: string;
  updated: string;
  customfield_10591?: {
    self: string;
    value: string;
    id: string;
    color: string;
  };
  worklogs: Array<{
    created_at: string;
    description: string;
    name: string;
    time_spent_hour: number;
    time_spent_human: string;
    time_spent_second: number;
    type: string;
    user_id: string;
  }>;
  // Additional fields for sprint tracking
  sprintId?: number;
  sprintName?: string;
  // Additional fields for week tracking
  weekKey?: string;
  weekLabel?: string;
  // Enhanced with changelog data
  changelog?: {
    histories: Array<{
      id: string;
      created: string;
      author: {
        accountId: string;
        displayName: string;
        emailAddress: string;
      };
      items: Array<{
        field: string;
        fieldtype: string;
        fieldId: string;
        from: string;
        fromString: string;
        to: string;
        toString: string;
      }>;
    }>;
  };
}

const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case "highest":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
    case "high":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
    case "medium":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
    case "low":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    case "lowest":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
};

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case "in progress":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    case "in testing dev (optional)":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
    case "in testing stg (optional)":
      return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200";
    case "done":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
};

export const taskColumns: ColumnDef<Task>[] = [
  {
    accessorKey: "task_name",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
          Task Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    size: 300,
    minSize: 250,
    maxSize: 350,
    cell: ({ row }) => {
      const task = row.original;
      return (
        <div className="space-y-1 max-w-xs">
          <div className="font-medium truncate" title={task.task_name}>
            {task.task_name}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={() => window.open(task.task_link, "_blank")}
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              View
            </Button>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "assignee",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
          Assignee
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    enableColumnFilter: true,
    filterFn: (row, id, value) => {
      const cellValue = row.getValue(id) as string;

      if (!cellValue) return false;

      const normalizedCellValue = cellValue.trim();

      // Handle array of selected values (multiple selection)
      if (Array.isArray(value)) {
        const result = value.some((v) => {
          const normalizedV = String(v).trim();
          return normalizedV === normalizedCellValue;
        });
        return result;
      }

      // Handle single value
      const normalizedValue = String(value || "").trim();
      const result = normalizedCellValue === normalizedValue;
      return result;
    },
  },
  {
    accessorKey: "sprintName",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
          Sprint
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const sprintName = row.getValue("sprintName") as string;
      return sprintName ? (
        <Badge variant="outline" className="text-xs">
          {sprintName}
        </Badge>
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
    filterFn: (row, id, value) => {
      const cellValue = row.getValue(id) as string;

      if (!cellValue) return false;

      const normalizedCellValue = cellValue.trim();

      // Handle array of selected values (multiple selection)
      if (Array.isArray(value)) {
        const result = value.some((v) => {
          const normalizedV = String(v).trim();
          return normalizedV === normalizedCellValue;
        });
        return result;
      }

      // Handle single value
      const normalizedValue = String(value || "").trim();
      const result = normalizedCellValue === normalizedValue;
      return result;
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <Badge className={getStatusColor(status)} variant="secondary">
          {status}
        </Badge>
      );
    },
    enableColumnFilter: true,
    filterFn: (row, id, value) => {
      const cellValue = row.getValue(id) as string;

      if (!cellValue) return false;

      const normalizedCellValue = cellValue.trim();

      // Handle array of selected values (multiple selection)
      if (Array.isArray(value)) {
        const result = value.some((v) => {
          const normalizedV = String(v).trim();
          return normalizedV === normalizedCellValue;
        });
        return result;
      }

      // Handle single value
      const normalizedValue = String(value || "").trim();
      const result = normalizedCellValue === normalizedValue;
      console.log("Single comparison result:", result, "for cell:", normalizedCellValue, "against:", normalizedValue);
      return result;
    },
  },
  {
    accessorKey: "time_spent_hours",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
          Time Spent
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const task = row.original;

      // Calculate hours based on the logic:
      // 1. If there's an "In Progress" worklog, use only that
      // 2. If no "In Progress" worklog, use task's total time_spent_hours
      // 3. If time_spent_hours is 0 but worklogs exist, sum all worklog hours
      // 4. If everything is 0 and status is not "To Do", mark as "Need Update"
      let displayHours = 0;
      let source = "";

      const inProgressWorklog = task.worklogs?.find((worklog) => worklog.type === "In Progress");

      if (inProgressWorklog) {
        // Use only the "In Progress" worklog hours
        displayHours = inProgressWorklog.time_spent_hour;
        source = "In Progress Only";
      } else if (task.time_spent_hours > 0) {
        // Use task's total hours if available
        displayHours = task.time_spent_hours;
        source = "From Worklogs";
      } else if (task.worklogs && task.worklogs.length > 0) {
        // Fallback: if time_spent_hours is 0 but worklogs exist, sum all worklog hours
        const worklogSum = task.worklogs.reduce((total, worklog) => total + (worklog.time_spent_hour || 0), 0);
        if (worklogSum > 0) {
          displayHours = worklogSum;
          source = "From Worklogs";
        }
      }

      // Only show "Need Update" if status is not "To Do" and no hours found
      if (!source && task.status.toLowerCase() !== "to do") {
        source = "Need Update";
      }

      // Additional validation: For "Done" tasks, if hours are very minimal (< 1 minute = 0.0167 hours), mark as "Need Update"
      if (task.status.toLowerCase() === "done" && displayHours > 0 && displayHours < 0.0167) {
        source = "Need Update";
      }

      // Define source colors
      const getSourceColor = (source: string) => {
        switch (source) {
          case "In Progress Only":
            return "text-blue-600 dark:text-blue-400";
          case "From Worklogs":
            return "text-green-600 dark:text-green-400";
          case "Need Update":
            return "text-red-600 dark:text-red-400";
          default:
            return "text-gray-600 dark:text-gray-400";
        }
      };

      return (
        <div className="space-y-1">
          <div className="text-sm text-muted-foreground">{formatDecimalHours(displayHours)}</div>
          {source && <div className={`text-xs ${getSourceColor(source)}`}>{source}</div>}
        </div>
      );
    },
  },
  {
    accessorKey: "sp",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
          Story Points
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const sp = row.getValue("sp") as number;
      return <div className="text-center">{sp}</div>;
    },
  },
  {
    accessorKey: "parent_name",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
          Parent
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const parentName = row.getValue("parent_name") as string;
      return parentName ? (
        <div className="truncate max-w-xs" title={parentName}>
          {parentName}
        </div>
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
    filterFn: (row, id, value) => {
      const cellValue = row.getValue(id) as string;

      if (!cellValue) return false;

      const normalizedCellValue = cellValue.trim();

      // Handle array of selected values (multiple selection)
      if (Array.isArray(value)) {
        const result = value.some((v) => {
          const normalizedV = String(v).trim();
          return normalizedV === normalizedCellValue;
        });
        return result;
      }

      // Handle single value
      const normalizedValue = String(value || "").trim();
      const result = normalizedCellValue === normalizedValue;
      return result;
    },
  },
  {
    accessorKey: "priority",
    header: "Priority",
    cell: ({ row }) => {
      const priority = row.getValue("priority") as string;
      return (
        <Badge className={getPriorityColor(priority)} variant="secondary">
          {priority}
        </Badge>
      );
    },
    enableColumnFilter: true,
    filterFn: (row, id, value) => {
      const cellValue = row.getValue(id) as string;

      if (!cellValue) return false;

      const normalizedCellValue = cellValue.trim();

      // Handle array of selected values (multiple selection)
      if (Array.isArray(value)) {
        const result = value.some((v) => {
          const normalizedV = String(v).trim();
          return normalizedV === normalizedCellValue;
        });
        return result;
      }

      // Handle single value
      const normalizedValue = String(value || "").trim();
      const result = normalizedCellValue === normalizedValue;
      return result;
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => {
      const type = row.getValue("type") as string;
      return <Badge variant="outline">{type}</Badge>;
    },
    enableColumnFilter: true,
    filterFn: (row, id, value) => {
      const cellValue = row.getValue(id) as string;

      if (!cellValue) return false;

      const normalizedCellValue = cellValue.trim();

      // Handle array of selected values (multiple selection)
      if (Array.isArray(value)) {
        const result = value.some((v) => {
          const normalizedV = String(v).trim();
          return normalizedV === normalizedCellValue;
        });
        return result;
      }

      // Handle single value
      const normalizedValue = String(value || "").trim();
      const result = normalizedCellValue === normalizedValue;
      return result;
    },
  },
  {
    accessorKey: "created",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
          Created
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const created = row.getValue("created") as string;
      return new Date(created).toLocaleDateString();
    },
  },
  {
    accessorKey: "updated",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
          Updated
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const updated = row.getValue("updated") as string;
      return new Date(updated).toLocaleDateString();
    },
  },
];

"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { LPLoading } from "@/components/lp-loading";
import { Suspense, useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import UnderConstructionPage from "./under-construction";
import { SprintOverview } from "./sprint-overview";
import { WeeklyOverview } from "./weekly-overview";

export function Tab() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState("sprint");

  // Load tab from URL on mount
  useEffect(() => {
    const tabFromUrl = searchParams.get("tab");
    if (tabFromUrl && ["sprint", "weekly", "bugs"].includes(tabFromUrl)) {
      setActiveTab(tabFromUrl);
    }
  }, [searchParams]);

  // Handle tab change and update URL
  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // Update URL without triggering navigation
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("tab", value);

    // Replace current URL with new tab parameter
    router.replace(`?${newSearchParams.toString()}`, { scroll: false });
  };

  return (
    <div className="space-y-6">
      {/* Tabs for team views */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full md:w-2/3 grid-cols-3 mb-4">
          <TabsTrigger value="sprint">Sprint</TabsTrigger>
          <TabsTrigger value="weekly">Weekly</TabsTrigger>
          <TabsTrigger value="bugs">Bugs</TabsTrigger>
        </TabsList>

        <TabsContent value="sprint" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <SprintOverview />
          </Suspense>
        </TabsContent>

        <TabsContent value="weekly" className="space-y-4">
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-60">
                <LPLoading />
              </div>
            }
          >
            <WeeklyOverview />
          </Suspense>
        </TabsContent>

        <TabsContent value="bugs" className="space-y-4">
          <UnderConstructionPage />
        </TabsContent>
      </Tabs>
    </div>
  );
}

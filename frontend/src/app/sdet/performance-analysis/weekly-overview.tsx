"use client";

import axios from "axios";
import { useMemo } from "react";
import { RootState } from "@/app/store";
import { useQuery } from "@tanstack/react-query";
import { useSelector } from "react-redux";
import { format } from "date-fns";
import { <PERSON><PERSON>, <PERSON>, CheckCircle, TrendingUp, ClipboardList } from "lucide-react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { API_BASE_URL } from "@/constants";
import { LPLoading } from "@/components/lp-loading";
import { DateFilter } from "@/components/date-filter";
import { formatDecimalHours } from "./utils/time-format";

interface WeeklyOverviewProps {
  jql?: string;
}

interface Task {
  assignee: string;
  created: string;
  parent_link: string;
  parent_name: string;
  priority: string;
  reporter: string;
  sp: number;
  status: string;
  task_link: string;
  task_name: string;
  time_spent_hours: number;
  time_spent_human: string;
  time_spent_second: number;
  type: string;
  updated: string;
  customfield_10591?: {
    self: string;
    value: string;
    id: string;
    color: string;
  };
  worklogs: Array<{
    created_at: string;
    description: string;
    name: string;
    time_spent_hour: number;
    time_spent_human: string;
    time_spent_second: number;
    type: string;
    user_id: string;
  }>;
  weekKey?: string;
  weekLabel?: string;
  changelog?: {
    histories: Array<{
      id: string;
      created: string;
      author: {
        accountId: string;
        displayName: string;
        emailAddress: string;
      };
      items: Array<{
        field: string;
        fieldtype: string;
        fieldId: string;
        from: string;
        fromString: string;
        to: string;
        toString: string;
      }>;
    }>;
  };
}

interface ChangelogResponse {
  expand: string;
  id: string;
  self: string;
  key: string;
  changelog: {
    startAt: number;
    maxResults: number;
    total: number;
    histories: Array<{
      id: string;
      author: {
        self: string;
        accountId: string;
        emailAddress: string;
        avatarUrls: any;
        displayName: string;
        active: boolean;
        timeZone: string;
        accountType: string;
      };
      created: string;
      items: Array<{
        field: string;
        fieldtype: string;
        fieldId: string;
        from: string | null;
        fromString: string | null;
        to: string | null;
        toString: string | null;
      }>;
    }>;
  };
}

interface DailyTimeEntry {
  date: string;
  hours: number;
  status: string;
  startTime: string;
  endTime?: string;
}

interface TaskTimeMapping {
  taskKey: string;
  assignee: string;
  dailyHours: DailyTimeEntry[];
  totalHours: number;
}

interface UserDailySummary {
  user: string;
  date: string;
  totalHours: number;
  tasks: Array<{
    taskKey: string;
    hours: number;
    status: string;
  }>;
}

const sdetTeamMembers = {
  "712020:b0f4204d-cf46-477e-b09e-************": "Naufal Athallah Iwel",
  "712020:66f39c08-1d2b-43db-856a-f15b448dc986": "Muhammad Zaenal Arifin",
  "712020:f75eae69-9502-4115-8613-d359b0d6611e": "Hanif Afianto DN",
  "712020:cf7bd17f-d166-4fcc-a70e-c709a201235e": "Adit Triyadi",
  "712020:59e84dde-4969-411e-8938-74c3a95144bf": "Septian Ega",
  "712020:52427388-9068-45a2-8d15-6a9221932e29": "andri.sukma",
  "5b62b8084a8e796a6c40f4af": "ayuanriyani",
};

export function WeeklyOverview({ jql }: WeeklyOverviewProps) {
  const { startDate, endDate } = useSelector((state: RootState) => state.dateFilter);

  const taskQuery = useQuery({
    queryKey: ["weekly-sdet-tasks", startDate, endDate],
    queryFn: async () => {
      const formattedStartDate = startDate ? format(new Date(startDate), "yyyy-MM-dd") : "";
      const formattedEndDate = endDate ? format(new Date(endDate), "yyyy-MM-dd") : "";

      // Step 1: Get tasks - Use SDET specific JQL with multiple conditions
      let sdetJql: string;

      if (jql) {
        sdetJql = jql;
      } else {
        // Handle different date scenarios for JQL
        if (formattedStartDate === formattedEndDate) {
          sdetJql = `project = GQA AND status CHANGED ON "${formattedStartDate}"`;
        } else {
          sdetJql = `project = GQA AND status CHANGED DURING ("${formattedStartDate}", "${formattedEndDate}")`;
        }
      }

      const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          jql: sdetJql,
        },
      });

      const tasks: Task[] = res.data.tasks;

      // Step 2: Get changelog for each task (intelligent hybrid processing)
      const taskMappings: TaskTimeMapping[] = [];

      try {
        const changelogPromises = tasks.map(async (task) => {
          const taskKey = extractIssueKey(task.task_link);

          // Check if there's an "In Progress" worklog
          const inProgressWorklog = task.worklogs?.find((worklog) => worklog.type === "In Progress");

          if (inProgressWorklog) {
            // 🔍 Case 1: Ada "In Progress" worklog → TEMBAK CHANGELOG API
            try {
              if (!taskKey) {
                console.warn(`Could not extract issue key from task_link: ${task.task_link}`);
                throw new Error("Invalid task key");
              }
              const changelog = await getTaskChangelog(taskKey);
              // Step 3: Mapping jam task per hari berdasarkan status changes
              return mapTaskDailyHours(task, changelog, formattedStartDate, formattedEndDate);
            } catch (error) {
              console.warn(error);
              // Fallback to worklog method
              return {
                taskKey: taskKey || task.task_link,
                assignee: task.assignee,
                dailyHours: [],
                totalHours: inProgressWorklog.time_spent_hour,
              };
            }
          } else {
            // Case 2-4: Tidak ada "In Progress" → TIDAK TEMBAK, gunakan worklog logic
            let worklogBasedHours = 0;

            if (task.time_spent_hours > 0) {
              // Case 2 & 4: Gunakan time_spent_hours
              worklogBasedHours = task.time_spent_hours;
            } else if (task.worklogs && task.worklogs.length > 0) {
              // Case 3: Sum all worklogs jika time_spent_hours = 0
              const worklogSum = task.worklogs.reduce((total, worklog) => total + (worklog.time_spent_hour || 0), 0);

              // Case 4: Jika worklog < 1 menit, gunakan time_spent_hours
              if (worklogSum < 0.0167) {
                // < 1 minute
                worklogBasedHours = task.time_spent_hours;
              } else {
                worklogBasedHours = worklogSum;
              }
            }

            const dailyHours: DailyTimeEntry[] = [];

            if (worklogBasedHours > 0 && task.worklogs && task.worklogs.length > 0) {
              // Use actual worklog dates if available
              const worklogsByDate = new Map<string, number>();

              task.worklogs.forEach((worklog) => {
                if (worklog.time_spent_hour > 0) {
                  const worklogDate = format(new Date(worklog.created_at), "yyyy-MM-dd");

                  // Check if worklog date is within our date range
                  if (worklogDate >= formattedStartDate && worklogDate <= formattedEndDate) {
                    const existingHours = worklogsByDate.get(worklogDate) || 0;
                    worklogsByDate.set(worklogDate, existingHours + worklog.time_spent_hour);
                  }
                }
              });

              // Create daily entries from worklog dates
              worklogsByDate.forEach((hours, date) => {
                dailyHours.push({
                  date: date,
                  hours: hours,
                  status: task.status,
                  startTime: task.updated,
                  endTime: task.updated,
                });
              });

              // STRICT FILTERING: If no worklogs fall within date range, DON'T show the task at all
              if (dailyHours.length === 0) {
                return {
                  taskKey: taskKey || task.task_link,
                  assignee: task.assignee,
                  dailyHours: [], // Empty daily hours means task won't appear
                  totalHours: 0, // Zero hours means task won't contribute to metrics
                };
              }
            } else if (worklogBasedHours > 0) {
              // Fallback: no worklogs, check if task update date is in range
              const taskUpdateDate = format(new Date(task.updated), "yyyy-MM-dd");

              if (taskUpdateDate >= formattedStartDate && taskUpdateDate <= formattedEndDate) {
                dailyHours.push({
                  date: taskUpdateDate,
                  hours: worklogBasedHours,
                  status: task.status,
                  startTime: task.updated,
                  endTime: task.updated,
                });
              } else {
                return {
                  taskKey: taskKey || task.task_link,
                  assignee: task.assignee,
                  dailyHours: [], // Empty daily hours means task won't appear
                  totalHours: 0, // Zero hours means task won't contribute to metrics
                };
              }
            } else {
              // Case 5: Task dengan 0 jam tapi ada aktivitas dalam date range
              let hasActivityInRange = false;

              const taskCreatedDate = format(new Date(task.created), "yyyy-MM-dd");
              const taskUpdatedDate = format(new Date(task.updated), "yyyy-MM-dd");

              if (
                (taskCreatedDate >= formattedStartDate && taskCreatedDate <= formattedEndDate) ||
                (taskUpdatedDate >= formattedStartDate && taskUpdatedDate <= formattedEndDate)
              ) {
                hasActivityInRange = true;
              }

              // Check if any worklog activity (even 0 hours) is in date range
              if (!hasActivityInRange && task.worklogs && task.worklogs.length > 0) {
                task.worklogs.forEach((worklog) => {
                  const worklogDate = format(new Date(worklog.created_at), "yyyy-MM-dd");
                  if (worklogDate >= formattedStartDate && worklogDate <= formattedEndDate) {
                    hasActivityInRange = true;
                  }
                });
              }

              // If task has activity in range, include it with minimal hours entry
              if (hasActivityInRange) {
                // Use updated date if it's in range, otherwise use created date
                let activityDate = taskUpdatedDate;
                if (taskUpdatedDate < formattedStartDate || taskUpdatedDate > formattedEndDate) {
                  if (taskCreatedDate >= formattedStartDate && taskCreatedDate <= formattedEndDate) {
                    activityDate = taskCreatedDate;
                  } else {
                    // Use the first worklog date if task dates are outside range
                    for (const worklog of task.worklogs || []) {
                      const worklogDate = format(new Date(worklog.created_at), "yyyy-MM-dd");
                      if (worklogDate >= formattedStartDate && worklogDate <= formattedEndDate) {
                        activityDate = worklogDate;
                        break;
                      }
                    }
                  }
                }

                dailyHours.push({
                  date: activityDate,
                  hours: task.time_spent_hours,
                  status: task.status,
                  startTime: task.updated,
                  endTime: task.updated,
                });
              }
            }

            return {
              taskKey: taskKey || task.task_link,
              assignee: task.assignee,
              dailyHours,
              totalHours: dailyHours.reduce((sum, entry) => sum + entry.hours, 0), // Calculate from dailyHours
            };
          }
        });

        const mappings = await Promise.all(changelogPromises);
        taskMappings.push(...mappings);
      } catch (error) {
        console.error("Error processing task mappings:", error);
      }

      // Step 4: Map user daily hours
      const userDailySummaries = mapUserDailyHours(taskMappings, tasks);

      // Create a set of valid task keys from taskMappings that have actual hours OR activity
      const validTaskKeys = new Set(
        taskMappings
          .filter((mapping) => mapping.dailyHours.length > 0) // Include ANY activity tasks (even 0 hours)
          .map((mapping) => mapping.taskKey)
      );

      // Only include tasks that have valid activity in the selected date range
      const filteredTasks = tasks.filter((task) => {
        const taskKey = extractIssueKey(task.task_link) || task.task_link;
        const isValid = validTaskKeys.has(taskKey);
        return isValid;
      });

      // Add date range info to filtered tasks
      const tasksWithDateRange = filteredTasks.map((task: Task) => ({
        ...task,
        key: extractIssueKey(task.task_link) || task.task_link, // Extract key from task_link for TaskTable
        weekKey: startDate && endDate ? `${startDate}-${endDate}` : "current-range",
        weekLabel: startDate && endDate ? `${startDate} to ${endDate}` : "Current Range",
      }));

      return {
        tasks: tasksWithDateRange,
        taskMappings: taskMappings.filter((mapping) => mapping.dailyHours.length > 0), // Include ANY activity tasks
        userDailySummaries,
      };
    },
    enabled: !!startDate && !!endDate,
  });

  const extractIssueKey = (taskLink: string): string => {
    // Extract issue key from URL like "https://lionparcel.atlassian.net/browse/GQA-3164"
    const match = taskLink.match(/\/browse\/([A-Z]+-\d+)$/);
    return match ? match[1] : "";
  };

  // Calculate individual engineer metrics from filtered taskMappings (more accurate)
  const engineerMetricsRecords = useMemo(() => {
    const allMembers = Object.fromEntries(
      Object.values(sdetTeamMembers).map((name) => [
        name,
        {
          totalHours: 0,
          tasks: new Set(),
          doneTasks: new Set(),
          storyPoints: 0,
          doneStoryPoints: 0,
          taskCount: 0,
          doneTaskCount: 0,
          weeklyHours: 0,
        },
      ])
    );

    if (!taskQuery.data?.taskMappings || !taskQuery.data?.tasks) {
      return allMembers;
    }

    const taskMappingsMap = new Map(taskQuery.data.taskMappings.map((mapping) => [mapping.taskKey, mapping]));

    // Process tasks and update metrics for members who have activity
    taskQuery.data.tasks.forEach((task) => {
      if (task.assignee === "") {
        return;
      }

      // Make sure the assignee exists in our records (should be in SDET team)
      if (!allMembers[task.assignee]) {
        return;
      }

      // Use the filtered taskMappings for accurate hour calculation
      const taskKey = extractIssueKey(task.task_link) || task.task_link;
      const taskMapping = taskMappingsMap.get(taskKey);

      // Use hours from taskMapping if available (more accurate for date range)
      const taskHours = taskMapping ? taskMapping.totalHours : 0;

      allMembers[task.assignee].totalHours += taskHours;
      allMembers[task.assignee].weeklyHours += taskHours;
      allMembers[task.assignee].tasks.add(task.task_link);
      allMembers[task.assignee].storyPoints += task.sp;

      if (task.status === "Done") {
        allMembers[task.assignee].doneStoryPoints += task.sp;
        allMembers[task.assignee].doneTasks.add(task.task_link);
      }

      allMembers[task.assignee].taskCount = allMembers[task.assignee].tasks.size;
      allMembers[task.assignee].doneTaskCount = allMembers[task.assignee].doneTasks.size;
    });

    return allMembers;
  }, [taskQuery.data?.tasks, taskQuery.data?.taskMappings]);

  const getTaskChangelog = async (taskKey: string): Promise<ChangelogResponse> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/jira/issue/${taskKey}/changelog`, {
        headers: {
          Accept: "application/json",
        },
      });
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch changelog for ${taskKey}:`, error);
      throw error;
    }
  };

  const mapTaskDailyHours = (
    task: Task,
    changelog: ChangelogResponse,
    startDate: string,
    endDate: string
  ): TaskTimeMapping => {
    const dailyHours: DailyTimeEntry[] = [];
    const histories = changelog.changelog.histories;

    // Sort histories by created date
    const sortedHistories = histories.sort((a, b) => new Date(a.created).getTime() - new Date(b.created).getTime());

    // Find status change items only
    const statusChanges = sortedHistories
      .map((history) => ({
        date: history.created,
        author: history.author.displayName,
        changes: history.items.filter((item) => item.field === "status"),
      }))
      .filter((history) => history.changes.length > 0);

    // Calculate working hours for each day based on status changes
    let currentStatus = "To Do";
    let currentStartTime: string | null = null;

    for (let i = 0; i < statusChanges.length; i++) {
      const change = statusChanges[i];
      const changeDate = format(new Date(change.date), "yyyy-MM-dd");

      if (changeDate >= startDate && changeDate <= endDate) {
        for (const statusChange of change.changes) {
          const fromStatus = statusChange.fromString || "To Do";
          const toStatus = statusChange.toString || "To Do";

          // If moving to "In Progress", start tracking time
          if (toStatus === "In Progress") {
            currentStartTime = change.date;
            currentStatus = toStatus;
          }

          // If moving from "In Progress" to another status, calculate hours
          if (fromStatus === "In Progress" && toStatus !== "In Progress" && currentStartTime) {
            const startTime = new Date(currentStartTime);
            const endTime = new Date(change.date);

            // Calculate hours between start and end time
            const hours = calculateWorkingHours(startTime, endTime);

            if (hours > 0) {
              dailyHours.push({
                date: changeDate,
                hours: hours,
                status: fromStatus,
                startTime: currentStartTime,
                endTime: change.date,
              });
            }

            currentStartTime = null;
          }

          currentStatus = toStatus;
        }
      }
    }

    // If task is still "In Progress" at the end of date range, calculate partial hours
    // BUT only if the "In Progress" start time is within the current date range
    if (currentStatus === "In Progress" && currentStartTime) {
      const startTime = new Date(currentStartTime);
      const startTimeDate = format(startTime, "yyyy-MM-dd");

      // Only calculate partial hours if the "In Progress" transition happened within our date range
      if (startTimeDate >= startDate && startTimeDate <= endDate) {
        const endOfRange = new Date(endDate + "T23:59:59");
        const hours = calculateWorkingHours(startTime, endOfRange);

        if (hours > 0) {
          dailyHours.push({
            date: endDate,
            hours: hours,
            status: "In Progress",
            startTime: currentStartTime,
            endTime: endOfRange.toISOString(),
          });
        }
      }
    }

    const totalHours = dailyHours.reduce((sum, entry) => sum + entry.hours, 0);

    return {
      taskKey: extractIssueKey(task.task_link) || task.task_link,
      assignee: task.assignee,
      dailyHours,
      totalHours,
    };
  };

  const calculateWorkingHours = (startTime: Date, endTime: Date): number => {
    const diffMs = endTime.getTime() - startTime.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);

    // Only count hours if they're on the same working day and within reasonable working hours
    const startDate = format(startTime, "yyyy-MM-dd");
    const endDate = format(endTime, "yyyy-MM-dd");

    // If different dates, don't calculate cross-day hours automatically
    if (startDate !== endDate) {
      return 0;
    }

    // Cap at 8 hours per day for same-day transitions
    return Math.min(diffHours, 8);
  };

  // Step 4: Mapping total jam user per hari
  const mapUserDailyHours = (taskMappings: TaskTimeMapping[], tasks: Task[]): UserDailySummary[] => {
    const userDailyMap = new Map<
      string,
      Map<string, { totalHours: number; tasks: Map<string, { taskKey: string; hours: number; status: string }> }>
    >();

    const taskMap = new Map(tasks.map((task) => [extractIssueKey(task.task_link) || task.task_link, task]));

    // Group by user and date, then by task to avoid duplicates
    taskMappings.forEach((taskMapping) => {
      if (!taskMapping.assignee) return;

      if (!userDailyMap.has(taskMapping.assignee)) {
        userDailyMap.set(taskMapping.assignee, new Map());
      }

      const userMap = userDailyMap.get(taskMapping.assignee)!;

      taskMapping.dailyHours.forEach((dailyEntry) => {
        if (!userMap.has(dailyEntry.date)) {
          userMap.set(dailyEntry.date, { totalHours: 0, tasks: new Map() });
        }

        const dayData = userMap.get(dailyEntry.date)!;

        // Get actual task status from the task, not from dailyEntry
        const actualTask = taskMap.get(taskMapping.taskKey);
        const actualStatus = actualTask ? actualTask.status : dailyEntry.status;

        // Check if task already exists for this day
        if (dayData.tasks.has(taskMapping.taskKey)) {
          // Accumulate hours for the same task
          const existingTask = dayData.tasks.get(taskMapping.taskKey)!;
          existingTask.hours += dailyEntry.hours;
          // Update status to actual task status
          existingTask.status = actualStatus;
        } else {
          // Add new task entry with actual task status
          dayData.tasks.set(taskMapping.taskKey, {
            taskKey: taskMapping.taskKey,
            hours: dailyEntry.hours,
            status: actualStatus, // Use actual task status, not intermediate status
          });
        }

        dayData.totalHours += dailyEntry.hours;
      });
    });

    // Convert to flat array
    const result: UserDailySummary[] = [];
    userDailyMap.forEach((userMap, user) => {
      userMap.forEach((dayData, date) => {
        result.push({
          user,
          date,
          totalHours: dayData.totalHours,
          tasks: Array.from(dayData.tasks.values()),
        });
      });
    });

    return result.sort((a, b) => a.date.localeCompare(b.date));
  };

  // Show error state for any API failures
  if (taskQuery.error) {
    return (
      <div className="flex flex-col items-center justify-center h-full animate-fade-in bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="flex items-center bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded mb-4 shadow-md animate-fade-in">
          <svg
            className="w-6 h-6 mr-3 text-red-500"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M18.364 5.636l-12.728 12.728M5.636 5.636l12.728 12.728"
            />
          </svg>
          <div>
            <span className="font-bold">Oops! Something went wrong.</span>
            <div className="text-sm mt-1">Failed to load task data. Please check your connection or try again.</div>
          </div>
        </div>
        <button
          className="bg-red-500 to-yellow-400 hover:bg-red-600 text-white font-bold py-2 px-6 rounded shadow-lg focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 transition"
          onClick={() => window.location.reload()}
          aria-label="Retry loading data"
        >
          Retry
        </button>
      </div>
    );
  }

  const sortedEngineers = Object.entries(engineerMetricsRecords)
    .map(([name, metrics]) => {
      const m = metrics as {
        totalHours: number;
        tasks: Set<any>;
        doneTasks: Set<any>;
        storyPoints: number;
        doneStoryPoints: number;
        taskCount: number;
        doneTaskCount: number;
        weeklyHours: number;
      };
      return {
        name,
        ...m,
        efficiency: m.taskCount > 0 ? m.totalHours / m.taskCount : 0,
      };
    })
    .sort((a, b) => {
      // Sort by weeklyHours descending, but keep members with 0 hours at the end
      if (a.weeklyHours === 0 && b.weeklyHours === 0) {
        return a.name.localeCompare(b.name);
      }
      if (a.weeklyHours === 0) return 1;
      if (b.weeklyHours === 0) return -1;
      return b.weeklyHours - a.weeklyHours;
    });

  // Helper function to calculate working days in date range (excluding weekends)
  const calculateWorkingDays = (startDate: string, endDate: string): number => {
    if (!startDate || !endDate) return 1;

    const start = new Date(startDate);
    const end = new Date(endDate);
    let workingDays = 0;

    for (let current = new Date(start); current <= end; current.setDate(current.getDate() + 1)) {
      // Skip weekends (Saturday = 6, Sunday = 0)
      if (current.getDay() !== 0 && current.getDay() !== 6) {
        workingDays++;
      }
    }

    return Math.max(workingDays, 1);
  };

  // Helper function to calculate target hours based on date range
  const calculateTargetHours = (startDate: string, endDate: string): number => {
    const workingDays = calculateWorkingDays(startDate, endDate);
    return workingDays * 8;
  };

  // Dynamic performance level calculation based on date range
  const getPerformanceLevel = (weeklyHours: number, startDate: string, endDate: string) => {
    const workingDays = calculateWorkingDays(startDate, endDate);
    const targetHours = workingDays * 8;

    if (weeklyHours === 0) {
      return {
        level: "Idle",
        color: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
      };
    }

    // Dynamic thresholds based on working days
    const lowThreshold = workingDays * 6;
    const highThreshold = workingDays * 7;

    if (weeklyHours < lowThreshold) {
      return {
        level: "Low Activity",
        color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      };
    }

    if (weeklyHours >= lowThreshold && weeklyHours < highThreshold) {
      return {
        level: "Medium Activity",
        color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      };
    }

    if (weeklyHours >= highThreshold && weeklyHours <= targetHours) {
      return {
        level: "High Activity",
        color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      };
    }

    return {
      level: "Over Activity",
      color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
    };
  };

  return (
    <div className="space-y-6">
      {/* Date Filter */}
      <DateFilter />

      {/* Show loading state for task data */}
      {taskQuery.isLoading ? (
        <div className="flex items-center justify-center py-20">
          <LPLoading />
        </div>
      ) : (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Team Members
                </div>
                {startDate && endDate && (
                  <div className="text-sm font-normal text-gray-500">
                    <div>Working Days: {calculateWorkingDays(startDate, endDate)}</div>
                    <div>Working Hours: {calculateTargetHours(startDate, endDate)}h</div>
                  </div>
                )}
              </CardTitle>

              {/* Dynamic Activity Level Legend */}
              {startDate && endDate && (
                <div className="flex flex-wrap gap-2 text-xs">
                  <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200" variant="secondary">
                    Idle: 0h
                  </Badge>
                  <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200" variant="secondary">
                    Low: &lt;{calculateWorkingDays(startDate, endDate) * 6}h
                  </Badge>
                  <Badge
                    className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                    variant="secondary"
                  >
                    Medium: {calculateWorkingDays(startDate, endDate) * 6}&lt;
                    {calculateWorkingDays(startDate, endDate) * 7}h
                  </Badge>
                  <Badge
                    className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                    variant="secondary"
                  >
                    High: {calculateWorkingDays(startDate, endDate) * 7}-{calculateTargetHours(startDate, endDate)}h
                  </Badge>
                  <Badge
                    className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
                    variant="secondary"
                  >
                    Over: &gt;{calculateTargetHours(startDate, endDate)}h
                  </Badge>
                </div>
              )}
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {sortedEngineers.map((engineer) => {
                  const performance = getPerformanceLevel(engineer.weeklyHours, startDate || "", endDate || "");

                  return (
                    <Card key={engineer.name}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <Avatar>
                              <AvatarFallback className="bg-primary text-primary-foreground tracking-wide flex items-center justify-center">
                                {(() => {
                                  const initials = engineer.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")
                                    .toUpperCase();
                                  // If initials are 3+ letters, only show first 2
                                  return initials.length > 2 ? initials.slice(0, 2) : initials;
                                })()}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <h3 className="font-semibold text-sm">{engineer.name}</h3>
                              <Badge className={performance.color} variant="secondary">
                                {performance.level}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="flex items-center font-medium">
                              <Clock className="h-3 w-3 mr-1" />
                              Total Hours
                            </span>
                            <span className="font-bold text-base text-green-600">
                              {formatDecimalHours(engineer.weeklyHours)}
                            </span>
                          </div>

                          <div className="flex items-center justify-between text-sm">
                            <span className="flex items-center">
                              <ClipboardList className="h-3 w-3 mr-1" />
                              Tasks Worked On
                            </span>
                            <span className="font-medium">{engineer.taskCount}</span>
                          </div>

                          <div className="flex items-center justify-between text-sm">
                            <span className="flex items-center">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Tasks (Done/Total)
                            </span>
                            <span className="font-medium">
                              {engineer.doneTaskCount}/{engineer.taskCount}
                            </span>
                          </div>

                          <div className="flex items-center justify-between text-sm">
                            <span className="flex items-center">
                              <TrendingUp className="h-3 w-3 mr-1" />
                              Story Points
                            </span>
                            <span className="font-medium">{engineer.storyPoints} SP</span>
                          </div>

                          {/* Daily Hours */}
                          {taskQuery.data?.userDailySummaries &&
                            (() => {
                              // Get daily summaries for this engineer
                              const engineerDailySummaries = taskQuery.data.userDailySummaries.filter(
                                (summary) => summary.user === engineer.name
                              );

                              // Group by date and sum hours
                              const dailyHoursMap = new Map<string, number>();
                              engineerDailySummaries.forEach((summary) => {
                                const existingHours = dailyHoursMap.get(summary.date) || 0;
                                dailyHoursMap.set(summary.date, existingHours + summary.totalHours);
                              });

                              // Generate all working days in the date range
                              const allWorkingDays: { date: string; hours: number }[] = [];
                              if (startDate && endDate) {
                                const start = new Date(startDate);
                                const end = new Date(endDate);

                                for (
                                  let current = new Date(start);
                                  current <= end;
                                  current.setDate(current.getDate() + 1)
                                ) {
                                  // Skip weekends (Saturday = 6, Sunday = 0)
                                  if (current.getDay() !== 0 && current.getDay() !== 6) {
                                    const dateString = format(current, "yyyy-MM-dd");
                                    const hours = dailyHoursMap.get(dateString) || 0;
                                    allWorkingDays.push({ date: dateString, hours });
                                  }
                                }
                              }

                              return allWorkingDays.length > 0 ? (
                                <div className="mt-3 pt-2 border-t border-gray-200 dark:border-gray-700">
                                  <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                                    Daily Hours:
                                  </div>
                                  <div className="space-y-1">
                                    {allWorkingDays.map(({ date, hours }) => (
                                      <div key={date} className="flex items-center justify-between text-xs">
                                        <span className="text-gray-600 dark:text-gray-400">
                                          {format(new Date(date), "MMM dd")}
                                        </span>
                                        <span
                                          className={`font-medium ${hours === 0 ? "text-gray-400" : "text-blue-600"}`}
                                        >
                                          {hours === 0 ? "-" : formatDecimalHours(hours)}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ) : null;
                            })()}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Changelog-based Time Tracking */}
          {taskQuery.data?.userDailySummaries && taskQuery.data.userDailySummaries.length > 0 && (
            <Card>
              <CardContent>
                <div className="space-y-4">
                  {/* Daily Summary View */}
                  <div className="grid gap-4">
                    {(() => {
                      // Group by date first
                      const dailyGroups = taskQuery.data.userDailySummaries.reduce((groups, summary) => {
                        if (!groups[summary.date]) {
                          groups[summary.date] = [];
                        }
                        groups[summary.date].push(summary);
                        return groups;
                      }, {} as Record<string, UserDailySummary[]>);

                      return Object.entries(dailyGroups)
                        .sort(([a], [b]) => a.localeCompare(b))
                        .map(([date, summaries]) => (
                          <Card key={date} className="border-l-4 border-l-blue-500">
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between mb-3">
                                <h4 className="font-semibold text-lg">
                                  {format(new Date(date), "EEEE, MMM dd, yyyy")}
                                </h4>
                                <Badge variant="outline" className="text-sm">
                                  {formatDecimalHours(summaries.reduce((total, s) => total + s.totalHours, 0))} total
                                </Badge>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                {summaries.map((summary) => (
                                  <div
                                    key={`${summary.user}-${summary.date}`}
                                    className="bg-card text-card-foreground rounded-xl border p-4"
                                  >
                                    <div className="flex items-center justify-between mb-2">
                                      <span className="font-medium text-sm">{summary.user}</span>
                                      <Badge variant="secondary" className="text-xs">
                                        {formatDecimalHours(summary.totalHours)}
                                      </Badge>
                                    </div>

                                    <div className="space-y-1">
                                      {summary.tasks.map((task, index) => (
                                        <div
                                          key={`${task.taskKey}-${index}`}
                                          className="flex items-center justify-between text-xs"
                                        >
                                          <span
                                            className="text-white font-mono cursor-pointer hover:underline"
                                            onClick={() => {
                                              // Find the task with matching key to get the link
                                              const taskWithLink = taskQuery.data?.tasks?.find(
                                                (t) => (extractIssueKey(t.task_link) || t.task_link) === task.taskKey
                                              );
                                              if (taskWithLink?.task_link) {
                                                window.open(taskWithLink.task_link, "_blank");
                                              }
                                            }}
                                          >
                                            {task.taskKey}
                                          </span>
                                          <div className="flex items-center space-x-2">
                                            <span className="font-medium">
                                              sp:{" "}
                                              {(() => {
                                                const taskWithSP = taskQuery.data?.tasks?.find(
                                                  (t) => (extractIssueKey(t.task_link) || t.task_link) === task.taskKey
                                                );
                                                const sp = taskWithSP ? taskWithSP.sp : 0;
                                                return sp === 0 ? "-" : sp;
                                              })()}
                                            </span>
                                            <span className="font-medium">{formatDecimalHours(task.hours)}</span>
                                            <Badge
                                              variant="secondary"
                                              className={`text-xs ${
                                                task.status.toLowerCase() === "in progress"
                                                  ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                                  : task.status.toLowerCase() === "done"
                                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                                  : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                                              }`}
                                            >
                                              {task.status}
                                            </Badge>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        ));
                    })()}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}

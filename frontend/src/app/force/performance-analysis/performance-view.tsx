"use client";

import { DateFilter } from "@/components/date-filter";
import { LPLoading } from "@/components/lp-loading";
import { TaskOverview } from "@/components/performance-analysis/task-overview";
import { Suspense } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { format } from "date-fns";

export function PerformanceView() {
  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );

  // Format dates for JIRA JQL (YYYY-MM-DD format)
  const formatDateForJQL = (dateString: string | undefined) => {
    if (!dateString) return null;
    return format(new Date(dateString), "yyyy-MM-dd");
  };

  const formattedStartDate = formatDateForJQL(startDate);
  const formattedEndDate = formatDateForJQL(endDate);

  // Construct JQL query with dynamic date range
  const jql = `project = Principal and status WAS IN ("In Progress", "In Testing Dev (Optional)", "In testing stg (OPTIONAL)") DURING (${formattedStartDate || 'startOfWeek()'}, ${formattedEndDate || 'endOfWeek()'}) ORDER BY assignee`;

  return (
    <>
      <DateFilter />
      <Suspense fallback={<LPLoading />}>
        <TaskOverview jql={jql} />
      </Suspense>
    </>
  );
}

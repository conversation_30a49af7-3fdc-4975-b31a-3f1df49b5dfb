"use client";
import {Bug, Check<PERSON><PERSON><PERSON>, Clock, Info, TrendingUp, User} from "lucide-react";
import {<PERSON>, CardContent, Card<PERSON>eader, CardTitle,} from "@/components/ui/card";
import {Badge} from "@/components/ui/badge";
import {Avatar, AvatarFallback} from "@/components/ui/avatar";
import {format} from "date-fns";
import {useSelector} from "react-redux";
import {RootState} from "@/app/store";
import {useSuspenseQuery} from "@tanstack/react-query";
import type {BugResponse} from "@/types/bug";
import axios from "axios";
import {API_BASE_URL} from "@/constants";
import {countWeekdaysExcludingHolidays, getGroupedActivity} from "@/lib/utils";
import {Popover, PopoverContent, PopoverTrigger} from "@/components/ui/popover";
import {Button} from "@/components/ui/button";

interface TeamMembersProps {
  dataWorklog: any[];
}
interface EngineerMetrics {
  totalHoursBreakdown: number;
  totalHoursOperation: number;
  totalHoursOnLeave: number;
  totalHoursProject: number;
  linkTicket: string[];
  storyPoints: number;
  storyPointsProject: number;
  efficiency: number;
  efficiencyProject: number;
  totalBug: number;
  totalStoryPointBug: number;
  performanceScore: number;
  name: string;
  userID?: string;
}

const productiveActivity = ["BREAKDOWN"];
const operationActivity = ["MEETING", "OPERATION", "ISSUE", "RELEASE", "GROOMING", "OTHERS"];
const projectActivity = ["PROJECT SA"];
const onLeaveActivity = ["ON LEAVE"];

// Utility: Extract initials for avatar fallback
const getInitials = (name: string) => {
  const initials = name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  return initials.length > 2 ? initials.slice(0, 2) : initials;
};

// Utility: Parse activity label from comment
const parseActivityLabel = (comment: string): string => {
  const match = comment.match(/\[([^\]]+)\]/);
  if (match) return match[1];
  return comment.split("\n")[0];
};

// Utility: Determine performance level
const getPerformanceLevel = (
    performanceScore: number,
    maxPerformanceScore: number
): { level: string; color: string } => {
  const percentage = ((performanceScore/maxPerformanceScore) * 100);
  if (percentage >= 100)
    return {
      level: "Exceptional",
      color:
          "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",
    };
  if (percentage >= 90)
    return {
      level: "Best",
      color:
          "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",
    };
  if (percentage >= 75)
    return {
      level: "Good",
      color:
          "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200",
    };
  if (percentage >= 60)
    return {
      level: "Low",
      color:
          "bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200",
    };
  return {
    level: "Worst",
    color: "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",
  };
};

const getStoryPointBug = (severity) => {
  if (!severity) return 1;
  if (severity.toLowerCase() === "high") return 5;
  if (severity.toLowerCase() === "medium") return 3;
  if (severity.toLowerCase() === "low") return 1;
  return 1;
}

export function TeamMembers({ dataWorklog }: TeamMembersProps) {
  const { startDate, endDate } = useSelector(
      (state: RootState) => state.dateFilter
  );

  const { data: bugData } = useSuspenseQuery<BugResponse>({
    queryKey: ["team-bugs", startDate, endDate],
    queryFn: async () => {
      const res = await axios.get(API_BASE_URL + `/api/jira/bug`, {
        headers: {
          Accept: "application/json",
        },
        params: {
          start_date: startDate
              ? format(new Date(startDate), "yyyy-MM-dd")
              : undefined,
          end_date: endDate
              ? format(new Date(endDate), "yyyy-MM-dd")
              : undefined,
        },
      });
      const emptyData: BugResponse = {
        bugs: [],
      };
      return res.data ?? emptyData;
    },
  });

  const endDateTimeDistribution = endDate ? endDate : startDate
  const totalDaysFilter = countWeekdaysExcludingHolidays(format(new Date(startDate ?? 0), "yyyy-MM-dd"), format(new Date(endDateTimeDistribution ?? 0), "yyyy-MM-dd"), );

  const totalHoursFilter = totalDaysFilter * 8;

  // Aggregate engineer metrics
  const engineerMetricsMap = dataWorklog.filter((item) => !item.name.includes("Bian")).reduce(
      (
          acc: Record<string, EngineerMetrics>,
          item: {
            name: string;
            user_id: string;
            work_log_time_spent_hour: number;
            link_ticket: string;
            sp: number;
            work_log_comment: string;
            work_log_date: string | number | Date;
            type: string;
          }
      ) => {
        const name = item.name;
        if (!acc[name]) {
          acc[name] = {
            totalHoursBreakdown: 0,
            totalHoursOperation: 0,
            linkTicket: [],
            storyPoints: 0,
            totalBug: 0,
            userID: item.user_id,
            totalHoursOnLeave: 0,
            performanceScore: 0,
            efficiency: 0,
            name: name,
            storyPointsProject: 0,
            totalHoursProject: 0,
            efficiencyProject: 0,
            totalStoryPointBug: 0,
          };
        }

        const metrics = acc[name];
        const activity = getGroupedActivity(item, parseActivityLabel(item.work_log_comment));
        if (productiveActivity.includes(activity)) {
          if (!metrics.linkTicket.includes(item.link_ticket)) {
            metrics.storyPoints += item.sp;
            metrics.linkTicket.push(item.link_ticket);
          }
          metrics.totalHoursBreakdown += item.work_log_time_spent_hour;
        }
        if (projectActivity.includes(activity)) {
          if (!metrics.linkTicket.includes(item.link_ticket)) {
            metrics.storyPointsProject += item.sp;
            metrics.linkTicket.push(item.link_ticket);
          }
          metrics.totalHoursProject += item.work_log_time_spent_hour;
        }
        if (operationActivity.includes(activity)) {
          metrics.totalHoursOperation += item.work_log_time_spent_hour;
        }
        if (onLeaveActivity.includes(activity)) {
          metrics.totalHoursOnLeave += item.work_log_time_spent_hour;
        }
        return acc;
      },
      {} as Record<string, EngineerMetrics>
  );
  // Convert map to array and compute efficiency and bug
  let engineers = Object.entries(engineerMetricsMap)
      .map(([name, metrics]) => {
        const m = metrics as EngineerMetrics;
        const storyPointCount = m.storyPoints;
        m.efficiency = (m.totalHoursBreakdown) > 0 ? storyPointCount / (m.totalHoursBreakdown) : 2;
        m.efficiencyProject = m.totalHoursProject > 0 ? m.storyPointsProject / m.totalHoursProject : 0;

        const bugList = bugData.bugs.filter((bug) => {
          return bug.pe_name === name && bug.accident_bug === "Bug Case Not Covered" && bug.parent_name !== "New Pusat Resolusi Phase 1";
        });
        m.totalBug = bugList.length;
        m.totalStoryPointBug = bugList.reduce((sum, bug) => {
          return sum + getStoryPointBug(bug.severity_bug)
        }, 0)
        return {
          ...m,
          storyPointCount
        };
      });


  if (engineers.length === 0) {
    return (
      <div className="flex flex-col gap-3">
        <Card>
          {/* Card Header */}
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Team Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                <User className="h-8 w-8 text-gray-400 dark:text-gray-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                No Team Members Found
              </h3>
              <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                No team member data is available for the selected date
                range. Try adjusting your date filter or check if work logs
                exist.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }
  function calculateEfficiencyScore(metrics) {
    const weightEfficiency = 40;
    const bonusEfficiency = 5;
    const slaEfficiency = 3;
    const maxEfficiency = Math.min(5, Math.max(...engineers.map(eng => eng.efficiency)));
    const engineerEfficiency = metrics.efficiency;
    if (engineerEfficiency >= slaEfficiency) {
      return weightEfficiency + (bonusEfficiency * (Math.min(5, engineerEfficiency) - slaEfficiency) / (maxEfficiency - slaEfficiency));
    }
    return engineerEfficiency / slaEfficiency * weightEfficiency;
  }
  function calculateProductivityScore(metrics) {
    const weightProductivity = 20;
    const bonusProductivity = 5;
    const availableWorkingHour = totalHoursFilter - metrics.totalHoursOnLeave;
    const totalProductivityHours = metrics.totalHoursBreakdown + metrics.totalHoursOperation + metrics.totalHoursProject;
    const ratio = totalProductivityHours / availableWorkingHour;
    return Math.min(ratio * weightProductivity, (weightProductivity + bonusProductivity));
  }
  function calculateBugScore(metrics) {
    const weightBug = 40;
    const normalizeStoryPointForBug = metrics.storyPoints / 3 || 1;
    return Math.max(0, (1 - (metrics.totalBug / normalizeStoryPointForBug))) * weightBug;
  }

  function calculatePerformanceScore(metrics) {
    const availableWorkingHour = totalHoursFilter - metrics.totalHoursOnLeave;
    if (availableWorkingHour <= 0) {
      return 50;
    }
    const productivityScore = calculateProductivityScore(metrics);
    const bugScore = calculateBugScore(metrics);
    const efficiencyScore = calculateEfficiencyScore(metrics);
    return productivityScore + bugScore + efficiencyScore;
  }
  engineers = engineers.map((engineer) => {
    return {
      ...engineer,
      performanceScore: calculatePerformanceScore(engineer),
    }
  }).sort((a, b) => b.performanceScore - a.performanceScore);

  const maxPerformanceScore = 100;

  return (
    <div className="flex flex-col gap-3">
      <Card>
        {/* Card Header */}
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Team Members Leaderboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {engineers.map((engineer) => {
              const performance = getPerformanceLevel(
                  engineer.performanceScore,
                  maxPerformanceScore
              );
              const initials = getInitials(engineer.name);
              return (
                  <Card key={engineer.name}>
                    <CardContent className="p-4">
                      {/* Profile & performance */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarFallback className="bg-primary text-primary-foreground tracking-wide flex items-center justify-center">
                              {initials}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-semibold text-sm">
                              {engineer.name}
                            </h3>
                            <Badge
                                className={performance.color}
                                variant="secondary"
                            >
                              {performance.level} Performer
                            </Badge>
                          </div>
                        </div>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <Info className="h-4 w-4" />
                              <span className="sr-only">Score Calculations</span>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-96 max-h-96 overflow-y-auto p-2">
                            <div className="font-semibold text-md">
                              Score Calculations
                            </div>
                            <div className="mt-3 flex flex-col space-y-2">
                              <div className="font-semibold text-sm">
                                Efficiency Score (40%) / Bonus (5%): {calculateEfficiencyScore(engineer).toFixed(2)}
                                <div className="font-normal text-xs">
                                  Efficiency / SLA Max * 40
                                </div>
                                <div className="font-normal text-xs">
                                  5 * ((Efficiency - 2.5) / (Max Efficiency - 2.5))
                                </div>
                              </div>
                              <div className="font-semibold text-sm">
                                Bug Score (40%): {calculateBugScore(engineer).toFixed(2)}
                                <div className="font-normal text-xs">
                                  (1 - (Bug / Normalisasi SP)) * 40
                                </div>
                              </div>
                              <div className="font-semibold text-sm">
                                Productivity Score (20%) / Bonus (5%): {calculateProductivityScore(engineer).toFixed(2)}
                                <div className="font-normal text-xs">
                                  MIN((total hour / working hour) * 20, (20 + 5))
                                </div>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Clock className="h-3 w-3 mr-1" />
                                Breakdown Hours
                              </span>
                          <span className="font-medium">
                                {engineer.totalHoursBreakdown.toFixed(1)}h
                              </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Story Points Breakdown
                              </span>
                          <span className="font-medium">
                                {engineer.storyPoints}
                              </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <TrendingUp className="h-3 w-3 mr-1" />
                                Efficiency Breakdown (SP/Hour)
                              </span>
                          <span className="font-medium">
                                {engineer.efficiency.toFixed(1)} SP/Hour
                              </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Clock className="h-3 w-3 mr-1" />
                                Operation Hours
                              </span>
                          <span className="font-medium">
                                {engineer.totalHoursOperation.toFixed(1)}h
                              </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Bug className="h-3 w-3 mr-1" />
                                Total Bug Case Not Covered
                              </span>
                          <span className="font-medium">
                                {engineer.totalBug} ({engineer.totalStoryPointBug} SP)
                              </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Clock className="h-3 w-3 mr-1" />
                                Project Hours
                              </span>
                          <span className="font-medium">
                                {engineer.totalHoursProject.toFixed(1)}h
                              </span>
                        </div>
                        <div className="flex items-center justify-between font-bold">
                              <span className="flex items-center ">
                                Performance Score
                              </span>
                          <span className="font-bold">
                                {engineer.performanceScore.toFixed(0)}
                              </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                              <span className="flex items-center ">
                                <Clock className="h-3 w-3 mr-1" />
                                On Leave Hours
                              </span>
                          <span className="font-medium">
                                {engineer.totalHoursOnLeave.toFixed(0)}h
                              </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

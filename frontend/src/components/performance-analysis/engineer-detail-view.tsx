"use client";

import { useSuspenseQuery } from "@tanstack/react-query";
import axios from "axios";
import type { EpicUser } from "@/types/epicuser";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import {
  ArrowLeft,
  Clock,
  Target,
  ExternalLink,
} from "lucide-react";
import { Card, CardHeader, CardTitle, CardContent } from "../ui/card";
import { DataTable } from "@/components/ui/data-table";
import { engineerIssueColumns } from "@/components/performance-analysis/engineer-issue-columns";

import { useRouter, useSearchParams } from "next/navigation";
import {
  formatTimeSpent, getBacklogEpics,
  getCalculateDoneStoryPoints, getDoneBacklogEpics, getDoneWeeklyEpics,
  getEpicStatusColor,
  getMondayWednesdayEpics, getMondayWednesdayStoryPoints,
  getThursdayFridayEpics, getThursdayFridayStoryPoints, getTotalBreakdownHours, getTotalStoryPointsFromBacklog
} from "@/lib/utils";
import { RootState } from "@/app/store";
import { useSelector } from "react-redux";
import { format } from "date-fns";
import { MetricCards } from "@/components/metric-cards";
import { useEffect } from "react";
import { API_BASE_URL } from "@/constants";
import { IssueWorklogs } from "./issue-work-logs";

export function EngineerDetailView() {
  const router = useRouter();
  const params = useSearchParams();
  const userID = params.get("user_id") || "";

  useEffect(() => {
    if (!userID) {
      router.push("/pe/performance-analysis");
    }
  }, [userID, router]);

  const { startDate, endDate } = useSelector(
    (state: RootState) => state.dateFilter
  );
  const filterDate = {
    start_date: format(new Date(startDate).setHours(0, 0, 0 ,0), "yyyy-MM-dd"),
    end_date: format(new Date(endDate).setHours(0, 0, 0 ,0), "yyyy-MM-dd"),
  }

  const { data } = useSuspenseQuery<EpicUser>({
    queryKey: ["epic-user", userID, startDate, endDate],
    queryFn: async () => {
      const urlParams = new URLSearchParams();
      if (startDate)
        urlParams.set("start_date", format(new Date(startDate), "yyyy-MM-dd"));
      if (endDate)
        urlParams.set("end_date", format(new Date(endDate), "yyyy-MM-dd"));
      const res = await axios.get(
        API_BASE_URL + `/api/jira/${userID}/epic?${urlParams.toString()}`
      );
      const emptyData: EpicUser = {
        epics: [],
      };
      return res.data ?? emptyData;
    },
  });

  // Extract all issues for the specific engineer
  const productEpics = data.epics.filter((epic) => {
    return !epic.epic_name.includes("Worklog");
  })

  const metricsSummary = [
    {
      title: "Story Points",
      value: getCalculateDoneStoryPoints(getThursdayFridayEpics(productEpics, filterDate), filterDate) + getCalculateDoneStoryPoints(getMondayWednesdayEpics(productEpics, filterDate), filterDate) + getCalculateDoneStoryPoints(getBacklogEpics(productEpics, filterDate), filterDate),
      description: "Total SP",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Total Hours in Epics",
      value: `${getTotalBreakdownHours(productEpics, filterDate).toFixed(2)}h`,
      description: "Logged time",
      icon: Clock,
      iconColor: "text-blue-600 dark:text-blue-400",
    },
    {
      title: "SP / Hour",
      value: (getTotalBreakdownHours(productEpics, filterDate) > 0 ? (getCalculateDoneStoryPoints(productEpics, filterDate) / getTotalBreakdownHours(productEpics, filterDate)) : 0).toFixed(2),
      description: "Average of SP Produced per Hour",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    }
  ];
  const metricsBacklog = [
    {
      title: "Backlog Epics",
      value: getBacklogEpics(productEpics, filterDate).length,
      description: "Total Backlog Epics from Last Week",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "SP from Backlog Epics",
      value: `${getTotalStoryPointsFromBacklog(productEpics, filterDate)} SP`,
      description: "Total SP from Backlog Epics from Last Week",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
  ];
  const metricsDoneFromBacklog = [
    {
      title: "Done Backlog Epics",
      value: getDoneBacklogEpics(productEpics, filterDate).length,
      description: "Total Backlog Epics Done from Last Week",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Done SP from Backlog Epics",
      value: `${getCalculateDoneStoryPoints(getBacklogEpics(productEpics, filterDate), filterDate)} SP`,
      description: "Total SP Done from Backlog Epics from Last Week",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
  ];
  const metricsAdding = [
    {
      title: "Epics Added at Monday-Wednesday",
      value: getMondayWednesdayEpics(productEpics, filterDate).length,
      description: "Total Epics Added at Monday-Wednesday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "SP from Epics Added at Monday-Wednesday",
      value: `${getMondayWednesdayStoryPoints(productEpics, filterDate)} SP`,
      description: "Total SP from Epics Added at Monday-Wednesday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Epics Added at Thursday-Friday",
      value: getThursdayFridayEpics(productEpics, filterDate).length,
      description: "Total Epics Added at Thursday-Friday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "SP from Epics Added at Thursday-Friday",
      value: `${getThursdayFridayStoryPoints(productEpics, filterDate)} SP`,
      description: "Total SP from Epics Added at Thursday-Friday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
  ];
  const metricsDoneFromAdding = [
    {
      title: "Done Epics Added at Monday-Wednesday",
      value: getDoneWeeklyEpics(getMondayWednesdayEpics(productEpics, filterDate)).length,
      description: "Total Epics Added at Monday-Wednesday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Done SP from Epics Added at Monday-Wednesday",
      value: `${getCalculateDoneStoryPoints(getMondayWednesdayEpics(productEpics, filterDate), filterDate)} SP`,
      description: "Total SP from Epics Added at Monday-Wednesday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Done Epics Added at Thursday-Friday",
      value:getDoneWeeklyEpics(getThursdayFridayEpics(productEpics, filterDate)).length,
      description: "Total Epics Added at Thursday-Friday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Done SP from Epics Added at Thursday-Friday",
      value: `${getCalculateDoneStoryPoints(getThursdayFridayEpics(productEpics, filterDate), filterDate)} SP`,
      description: "Total SP from Epics Added at Thursday-Friday",
      icon: Target,
      iconColor: "text-purple-600 dark:text-purple-400",
    },
  ];


  return (
    <>
      {/* Header */}
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between px-2 sm:px-0">
        <div className="flex items-center gap-2 sm:gap-4 w-full">
          <Button
            variant="ghost"
            size="sm"
            className="w-fit"
            onClick={() => window.history.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="flex-1 min-w-0 w-full">
            <h1 className="text-xl sm:text-2xl font-bold capitalize text-ellipsis">
              {data.epics[0]?.issues[0]?.name}
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Individual Performance Analysis
            </p>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <MetricCards metrics={metricsSummary} columns={3} section={"Summary"} />
      <MetricCards metrics={metricsBacklog} columns={2} section={"Backlog"} />
      <MetricCards metrics={metricsDoneFromBacklog} columns={2} section={"Done Backlog"} />
      <MetricCards metrics={metricsAdding} columns={4} section={"Epics Added"} />
      <MetricCards metrics={metricsDoneFromAdding} columns={4} section={"Done Epics Added"} />

      {/* Epic Cards with Issue Tables */}
      {data.epics.length > 0 ? (
        <div className="space-y-4 sm:space-y-6">
          {data.epics.map((epic) => {
            const engineerIssuesInEpic = epic.issues;
            const breakdownStatus = engineerIssuesInEpic.every((issue) => issue.status === "Done") ? "Done" : "In Progress";

            // EpicUserEpic does not have epic_link, so skip or handle gracefully
            const epicLink = epic.epic_link;

            return (
              <Card key={epic.epic_link} className="border-2">
                <CardHeader>
                  <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-wrap items-center gap-2 mb-2 relative">
                        <CardTitle
                          className={`text-base sm:text-lg px-2 py-1 w-fit border-l-6 font-semibold ${
                            epic.epic_name.toLowerCase().startsWith("worklog")
                              ? "border-blue-600 dark:border-blue-400"
                              : "border-violet-600 dark:border-violet-400"
                          }`}
                        >
                          {epic.epic_name}
                        </CardTitle>
                        <Badge
                          className={
                            getEpicStatusColor(breakdownStatus) +
                            " text-xs px-2 py-1"
                          }
                          variant="secondary"
                        >
                          {breakdownStatus.toUpperCase()}
                        </Badge>
                      </div>
                      <div className="flex flex-col gap-2 text-xs sm:text-sm text-muted-foreground">
                        {/* First row: Reporter */}
                        <div className="flex items-center gap-2 w-full">
                          <div className="flex items-center gap-1 min-w-0 flex-1">
                            <span className="font-semibold text-foreground text-xs whitespace-nowrap">
                              Reporter:
                            </span>
                            <Badge
                              variant="outline"
                              className="px-2 py-0.5 text-xs truncate"
                            >
                              {epic.reporter}
                            </Badge>
                          </div>
                        </div>
                        {/* Second row: Grooming | Done Breakdown */}
                        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 sm:flex-wrap">
                          {epic.grooming_date && (
                            <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Grooming:
                              </span>
                              <Badge
                                variant="outline"
                                className="px-2 py-0.5 text-xs"
                              >
                                {new Intl.DateTimeFormat('en-GB', {
                                  day: '2-digit',
                                  month: 'short',
                                  year: 'numeric'
                                }).format(new Date(
                                    epic.grooming_date
                                ))}
                              </Badge>
                            </div>
                          )}
                          {epic.done_break_down_date && (
                            <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Done Breakdown:
                              </span>
                              <Badge
                                variant="outline"
                                className="px-2 py-0.5 text-xs"
                              >
                                {new Intl.DateTimeFormat('en-GB', {
                                  day: '2-digit',
                                  month: 'short',
                                  year: 'numeric'
                                }).format(new Date(
                                    epic.done_break_down_date
                                ))}
                              </Badge>
                            </div>
                          )}
                        </div>
                        {/* Third row: Breakdown Duration */}
                        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between w-full">
                          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 flex-1 min-w-0">
                            <div className="flex items-center gap-1">
                                <span className="font-semibold text-foreground text-xs">
                                  Breakdown:
                                </span>
                              <Badge
                                  variant="outline"
                                  className="px-2 py-0.5 text-xs"
                              >
                                {formatTimeSpent(
                                    epic.break_down_duration_human
                                )}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        {/* Fourth row: Detailing Hour & Operation Hour */}
                        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between w-full">
                          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 flex-1 min-w-0">
                            <div className="flex items-center gap-1">
                                <span className="font-semibold text-foreground text-xs">
                                  Detailing:
                                </span>
                              <Badge
                                  variant="outline"
                                  className="px-2 py-0.5 text-xs"
                              >
                                {formatTimeSpent(
                                    epic.detailing_duration_human
                                )}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-1">
                                <span className="font-semibold text-foreground text-xs">
                                  Operation:
                                </span>
                              <Badge
                                  variant="outline"
                                  className="px-2 py-0.5 text-xs"
                              >
                                {formatTimeSpent(epic.operation_hour_human)}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        {/* Fifth row: Total SP | Issues | Done SP | Backlog SP */}
                        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between w-full">
                          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4 flex-1 min-w-0">
                            <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Total SP:
                              </span>
                              <Badge
                                variant="outline"
                                className="px-2 py-0.5 text-xs"
                              >
                                {epic.sp}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Issues:
                              </span>
                              <Badge
                                variant="outline"
                                className="px-2 py-0.5 text-xs"
                              >
                                {epic.issues.length}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Done SP:
                              </span>
                              <Badge
                                variant="outline"
                                className="px-2 py-0.5 text-xs"
                              >
                                {epic.issues
                                  .filter((issue) => issue.status === "Done")
                                  .reduce((sp, issue) => {
                                    return (sp += issue.sp);
                                  }, 0)}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                Backlog SP:
                              </span>
                              <Badge
                                variant="outline"
                                className="px-2 py-0.5 text-xs"
                              >
                                {epic.issues
                                  .filter((issue) => issue.status !== "Done")
                                  .reduce((sp, issue) => {
                                    return (sp += issue.sp);
                                  }, 0)}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className="font-semibold text-foreground text-xs">
                                  SP/Hour:
                              </span>
                              <Badge
                                  variant="outline"
                                  className="px-2 py-0.5 text-xs"
                              >
                                {epic.detailing_duration_hour === 0 ? 0 : (epic.issues
                                    .filter((issue) => issue.status === "Done")
                                    .reduce((sp, issue) => {
                                      return sp += issue.sp
                                    }, 0) / epic.detailing_duration_hour).toFixed(2)
                                }
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-fit"
                      onClick={() => window.open(epicLink, "_blank")}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <DataTable
                    columns={engineerIssueColumns}
                    data={engineerIssuesInEpic}
                    initialSort={[{ id: "status", desc: false }]}
                    renderExpandedRow={(row) => {
                      return <IssueWorklogs worklogs={row.original.worklogs} />;
                    }}
                  />
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground">No epic data available</p>
          </CardContent>
        </Card>
      )}

      {/* Engineer Bugs Section */}
    </>
  );
}

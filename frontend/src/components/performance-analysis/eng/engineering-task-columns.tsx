import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { getEngTaskColor } from "@/lib/utils";
import { Task } from "@/types/eng/epic";
import { ColumnDef } from "@tanstack/react-table";
import { ExternalLink } from "lucide-react";

const taskColumns: (keyof Task)[] = [
  "key",
  "url",
  "status",
  "summary",
  "assignee",
  "sp",
  "expected_start_date",
  "actual_start_date",
  "expected_end_date",
  "actual_end_date",
  "ontime",
  "hours",
  "hour_per_sp",
  "additional",
  "bug",
];

// Helper to check if the field is a date
const dateFields = new Set<keyof Task>([
  "expected_start_date",
  "actual_start_date",
  "expected_end_date",
  "actual_end_date",
]);

export const engineeringTaskList: ColumnDef<Task>[] = taskColumns.map((key) => ({
  accessorKey: key,
  header: key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase()),
  cell: ({ row }) => {
    const value = row.original[key];
    if (key === "url" && typeof value === "string") {
      return (
        <Button
          variant="ghost"
          size="sm"
          className="w-fit"
          onClick={() => window.open(value, "_blank")}
        >
          <ExternalLink className="h-4 w-4" />
        </Button>
      );
    }
    if (key === "status" && typeof value === "string") {
      return (
        <Badge
          className={`${getEngTaskColor(value)} text-xs shrink-0`}
          variant="secondary"
        >
          {value}
        </Badge>
      );
    }
    const isDate = dateFields.has(key);
    const formattedValue =
      isDate && value
        ? new Intl.DateTimeFormat("en-GB", {
            day: "2-digit",
            month: "short",
            year: "numeric",
          }).format(new Date(value))
        : String(value ?? "-");

    return (
      <div
        className="truncate max-w-[120px] sm:max-w-md"
        title={formattedValue}
      >
        {formattedValue}
      </div>
    );
  },
  enableSorting: true,
  enableColumnFilter: true,
}));

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { getEngTaskColor } from "@/lib/utils";
import {
  EngLeaderBoard,
  MemberLeaderBoard,
  TaskLeaderBoard,
} from "@/types/eng/leaderboard";
import { ColumnDef } from "@tanstack/react-table";
import { ExternalLink } from "lucide-react";

export const engLeaderboardColumn: (keyof EngLeaderBoard)[] = [
  "name",
  "sp",
  "hours",
  "hours_per_sp",
  "bug",
];

export const qaLeaderboardColumn: (keyof MemberLeaderBoard)[] = [
  "name",
  "sp",
  "hours",
  "hours_per_sp",
];

export const memberTaskColumn: (keyof TaskLeaderBoard)[] = [
  "summary",
  "url",
  "status",
  "sp",
  "expected_start_date",
  "actual_start_date",
  "expected_end_date",
  "actual_end_date",
  "hours",
  "hours_per_sp",
];

// Helper to check if the field is a date
const dateFields = new Set<keyof TaskLeaderBoard>([
  "expected_start_date",
  "expected_end_date",
]);

const dateTimeFields = new Set<keyof TaskLeaderBoard>([
  "actual_start_date",
  "actual_end_date",
]);

export const engineeringEngineerLeaderBoard: ColumnDef<EngLeaderBoard>[] =
  engLeaderboardColumn.map((key) => ({
    accessorKey: key,
    header:
      key == "hours_per_sp"
        ? "Speed (Hours/SP)"
        : key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase()),
    cell: ({ row }) => {
      const value = row.original[key];
      return (
        <div
          className="truncate max-w-[120px] sm:max-w-md"
          title={String(value)}
        >
          {String(value)}
        </div>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  }));

export const engineeringQALeaderBoard: ColumnDef<MemberLeaderBoard>[] =
  qaLeaderboardColumn.map((key) => ({
    accessorKey: key,
    header:
      key == "hours_per_sp"
        ? "Speed (Hours/SP)"
        : key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase()),
    cell: ({ row }) => {
      const value = row.original[key];
      return (
        <div
          className="truncate max-w-[120px] sm:max-w-md"
          title={String(value)}
        >
          {String(value)}
        </div>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  }));

export const engineeringTaskLeaderBoard: ColumnDef<TaskLeaderBoard>[] =
  memberTaskColumn.map((key) => ({
    accessorKey: key,
    header:
      key == "hours_per_sp"
        ? "Speed (Hours/SP)"
        : key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase()),
    cell: ({ row }) => {
      const value = row.original[key];
      if (key === "url" && typeof value === "string") {
        return (
          <Button
            variant="ghost"
            size="sm"
            className="w-fit"
            onClick={() => window.open(value, "_blank")}
          >
            <ExternalLink className="h-4 w-4" />
          </Button>
        );
      }

      if (key === "status" && typeof value === "string") {
        return (
          <Badge
            className={`${getEngTaskColor(value)} text-xs shrink-0`}
            variant="secondary"
          >
            {value}
          </Badge>
        );
      }

      let formattedValue: string;

      if (dateTimeFields.has(key) && value) {
        formattedValue = new Intl.DateTimeFormat("en-GB", {
          day: "2-digit",
          month: "short",
          year: "numeric",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
        }).format(new Date(value));
      } else if (dateFields.has(key) && value) {
        formattedValue = new Intl.DateTimeFormat("en-GB", {
          day: "2-digit",
          month: "short",
          year: "numeric",
        }).format(new Date(value));
      } else {
        formattedValue = String(value ?? "-");
      }

      return (
        <div
          className="truncate max-w-[120px] sm:max-w-md"
          title={String(formattedValue)}
        >
          {String(formattedValue)}
        </div>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  }));

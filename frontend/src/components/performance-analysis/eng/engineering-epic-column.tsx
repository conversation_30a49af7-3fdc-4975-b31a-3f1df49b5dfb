import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { getEngTaskColor } from "@/lib/utils";
import { Epic } from "@/types/eng/epic";
import { ColumnDef } from "@tanstack/react-table";
import { ExternalLink } from "lucide-react";

const epicColumns: (keyof Epic)[] = [
  "name",
  "url",
  "status",
  "reporter",
  "expected_release_date",
  "fix_version",
];

// Helper to check if the field is a date
const dateFields = new Set<keyof Epic>(["expected_release_date"]);

export const engineeringEpicList: ColumnDef<Epic>[] = epicColumns.map(
  (key) => ({
    accessorKey: key,
    header: key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase()),
    cell: ({ row }) => {
      const value = row.original[key];
      if (key === "url" && typeof value === "string") {
        return (
          <Button
            variant="ghost"
            size="sm"
            className="w-fit"
            onClick={() => window.open(value, "_blank")}
          >
            <ExternalLink className="h-4 w-4" />
          </Button>
        );
      }
      if (key === "status" && typeof value === "string") {
        return (
          <Badge
            className={`${getEngTaskColor(value)} text-xs shrink-0`}
            variant="secondary"
          >
            {value}
          </Badge>
        );
      }
      const isDate = dateFields.has(key);
      const formattedValue =
        isDate && typeof value === "string"
          ? new Intl.DateTimeFormat("en-GB", {
              day: "2-digit",
              month: "short",
              year: "numeric",
            }).format(new Date(value))
          : String(value ?? "-");

      return (
        <div
          className="truncate max-w-[120px] sm:max-w-md"
          title={formattedValue}
        >
          {formattedValue}
        </div>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
  })
);

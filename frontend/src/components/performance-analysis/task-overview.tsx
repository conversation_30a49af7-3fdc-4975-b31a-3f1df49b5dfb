"use client";

import axios from "axios";
import { RootState } from "@/app/store";
import { useSuspenseQuery, useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON>, CheckCircle, <PERSON><PERSON><PERSON>Up, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, FolderOpen } from "lucide-react";
import {
    Card,
    CardHeader,
    CardTitle,
    CardContent,
} from "../ui/card";
import { Badge } from "../ui/badge";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { Skeleton } from "../ui/skeleton";
import { API_BASE_URL } from "@/constants";
import { TaskTable } from "@/components/task-table";
import { MetricCards } from "@/components/metric-cards";
import {differenceInCalendarDays} from "date-fns";

interface TaskOverviewProps {
    jql?: string;
}

interface Task {
    assignee: string;
    created: string;
    labels: string[];
    parent_link: string;
    parent_name: string;
    priority: string;
    reporter: string;
    sp: number;
    status: string;
    task_link: string;
    task_name: string;
    time_spent_hours: number;
    time_spent_human: string;
    time_spent_second: number;
    type: string;
    updated: string;
    worklogs: Array<{
        created_at: string;
        description: string;
        name: string;
        time_spent_hour: number;
        time_spent_human: string;
        time_spent_second: number;
        type: string;
        user_id: string;
    }>;
}

interface TaskResponse {
    tasks: Task[];
}

interface CountResponse {
    count: number;
}

const METRIC_CONFIGS = [
    {
        jql: 'parent=PRIN-990 AND status = "To Do" AND summary !~ "QA"',
        name: "API Fixer Implementation",
        icon: Wrench,
        iconColor: "text-blue-600",
        description: "Engineer To Do Tasks"
    },
    {
        jql: 'parent=PRIN-1264 AND status = "To Do" AND summary !~ "QA"',
        name: "Adhoc",
        icon: AlertTriangle,
        iconColor: "text-yellow-600",
        description: "Engineer To Do Tasks"
    },
    {
        jql: 'parent=PRIN-1196 AND status = "To Do" AND summary !~ "QA"',
        name: "Issue",
        icon: Bug,
        iconColor: "text-red-600",
        description: "Engineer To Do Tasks"
    },
    {
        jql: 'summary~"PROJECT-SA 001" AND status = "To Do" AND summary !~ "QA"',
        name: "Project",
        icon: FolderOpen,
        iconColor: "text-green-600",
        description: "Engineer To Do Tasks"
    },
    {
        jql: 'parent=PRIN-990 AND status = "To Do" AND summary ~ "QA"',
        name: "API Fixer Implementation",
        icon: Wrench,
        iconColor: "text-blue-600",
        description: "QA To Do Tasks"
    },
    {
        jql: 'parent=PRIN-1264 AND status = "To Do" AND summary ~ "QA"',
        name: "Adhoc",
        icon: AlertTriangle,
        iconColor: "text-yellow-600",
        description: "QA To Do Tasks"
    },
    {
        jql: 'parent=PRIN-1196 AND status = "To Do" AND summary ~ "QA"',
        name: "Issue",
        icon: Bug,
        iconColor: "text-red-600",
        description: "QA To Do Tasks"
    },
    {
        jql: 'summary~"PROJECT-SA 001" AND status = "To Do" AND summary ~ "QA"',
        name: "Project",
        icon: FolderOpen,
        iconColor: "text-green-600",
        description: "QA To Do Tasks"
    }
];

export function TaskOverview({ jql }: TaskOverviewProps) {
    const pathname = usePathname();
    const role = pathname.split("/")[1] || "pe";

    const { startDate, endDate } = useSelector(
        (state: RootState) => state.dateFilter
    );

    const { data, isError } = useSuspenseQuery({
        queryKey: ["tasks", startDate, endDate, role],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/task`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: jql || `project = Principal and status WAS IN ("In Progress", "In Testing Dev (Optional)", "In testing stg (OPTIONAL)") DURING (startOfWeek(), endOfWeek()) ORDER BY assignee`,
                },
            });
            return res.data as TaskResponse;
        },
    });

    // Metric cards queries - called individually to follow React hooks rules
    const apiFixerQuery = useQuery({
        queryKey: ["count", METRIC_CONFIGS[0].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[0].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const adhocQuery = useQuery({
        queryKey: ["count", METRIC_CONFIGS[1].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[1].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const issueQuery = useQuery({
        queryKey: ["count", METRIC_CONFIGS[2].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[2].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const projectQuery = useQuery({
        queryKey: ["count", METRIC_CONFIGS[3].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[3].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const apiFixerQueryQA = useQuery({
        queryKey: ["count", METRIC_CONFIGS[4].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[4].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const adhocQueryQA = useQuery({
        queryKey: ["count", METRIC_CONFIGS[5].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[5].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const issueQueryQA = useQuery({
        queryKey: ["count", METRIC_CONFIGS[6].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[6].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const projectQueryQA = useQuery({
        queryKey: ["count", METRIC_CONFIGS[7].jql],
        queryFn: async () => {
            const res = await axios.get(API_BASE_URL + `/api/jira/count`, {
                headers: {
                    Accept: "application/json",
                },
                params: {
                    jql: METRIC_CONFIGS[7].jql,
                },
            });
            return res.data as CountResponse;
        },
    });

    const metricQueries = [apiFixerQuery, adhocQuery, issueQuery, projectQuery, apiFixerQueryQA, adhocQueryQA, issueQueryQA, projectQueryQA];
    const isAnyMetricLoading = metricQueries.some(query => query.isLoading);

    // Split metrics into Engineer (0-3) and QA (4-7) parts
    const engineerMetrics = METRIC_CONFIGS.slice(0, 4).map((config, index) => ({
        title: config.name,
        value: metricQueries[index].data?.count ?? 0,
        icon: config.icon,
        iconColor: config.iconColor,
        description: config.description,
    }));

    const qaMetrics = METRIC_CONFIGS.slice(4, 8).map((config, index) => ({
        title: config.name,
        value: metricQueries[index + 4].data?.count ?? 0,
        icon: config.icon,
        iconColor: config.iconColor,
        description: config.description,
    }));


    // Skeleton metrics for loading state
    const skeletonEngineerMetrics = METRIC_CONFIGS.slice(0, 4).map((config) => ({
        title: config.name,
        value: <Skeleton className="h-8 w-16" />,
        icon: config.icon,
        iconColor: config.iconColor,
        description: config.description,
    }));

    const skeletonQaMetrics = METRIC_CONFIGS.slice(4, 8).map((config) => ({
        title: config.name,
        value: <Skeleton className="h-8 w-16" />,
        icon: config.icon,
        iconColor: config.iconColor,
        description: config.description,
    }));

    if (isError)
        return (
            <div className="flex flex-col items-center justify-center h-full animate-fade-in bg-gradient-to-br from-gray-50 to-gray-100">
                <div className="flex items-center bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded mb-4 shadow-md animate-fade-in">
                    <svg
                        className="w-6 h-6 mr-3 text-red-500"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M18.364 5.636l-12.728 12.728M5.636 5.636l12.728 12.728"
                        />
                    </svg>
                    <div>
                        <span className="font-bold">Oops! Something went wrong.</span>
                        <div className="text-sm mt-1">
                            We couldn't load the data. Please check your connection or try
                            again.
                        </div>
                    </div>
                </div>
                <button
                    className="bg-red-500 to-yellow-400 hover:bg-red-600 text-white font-bold py-2 px-6 rounded shadow-lg focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 transition"
                    onClick={() => window.location.reload()}
                    aria-label="Retry loading data"
                >
                    Retry
                </button>
            </div>
        );

    // Calculate individual engineer metrics from tasks
    const engineerMetricsRecords = data.tasks.reduce((acc, task) => {
        if (task.assignee === "") {
            return acc; // Skip tasks without an assignee
        }
        if (!acc[task.assignee]) {
            acc[task.assignee] = {
                totalHours: 0,
                tasks: new Set(),
                storyPoints: 0,
                taskCount: 0,
            };
        }

        acc[task.assignee].totalHours += task.time_spent_hours;
        acc[task.assignee].tasks.add(task.task_link);
        acc[task.assignee].storyPoints += task.sp;
        acc[task.assignee].taskCount = acc[task.assignee].tasks.size;

        return acc;
    }, {} as Record<string, any>);

    const sortedEngineers = Object.entries(engineerMetricsRecords)
        .map(([name, metrics]) => {
            const m = metrics as {
                totalHours: number;
                tasks: Set<any>;
                storyPoints: number;
                taskCount: number;
            };
            return {
                name,
                ...m,
                efficiency: m.taskCount > 0 ? m.totalHours / m.taskCount : 0,
            };
        })
        .sort((a, b) => b.totalHours - a.totalHours);

    const endDateTimeDistribution = endDate ? endDate : startDate
    const totalDaysFilter = differenceInCalendarDays(new Date(endDateTimeDistribution), new Date(startDate)) + 1;
    const totalHoursFilter = totalDaysFilter * 8;
    const getPerformanceLevel = (hours: number) => {
        const percentage = (hours / totalHoursFilter) * 100;
        if (percentage >= 80)
            return {
                level: "High",
                color:
                    "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            };
        if (percentage >= 60)
            return {
                level: "Medium",
                color:
                    "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            };
        return {
            level: "Low",
            color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
        };
    };

    return (
        <div className="space-y-6">
            {/* Engineer Metric Cards */}
            <MetricCards
                metrics={isAnyMetricLoading ? skeletonEngineerMetrics : engineerMetrics}
                columns={4}
                section="Engineer Task Metrics"
            />

            {/* QA Metric Cards */}
            <MetricCards
                metrics={isAnyMetricLoading ? skeletonQaMetrics : qaMetrics}
                columns={4}
                section="QA Task Metrics"
            />

            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center">
                        <User className="h-5 w-5 mr-2" />
                        Team Members
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {sortedEngineers.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-12 text-center">
                            <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                                <User className="h-8 w-8 text-gray-400 dark:text-gray-600" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                                No Team Members Found
                            </h3>
                            <p className="text-gray-500 dark:text-gray-400 max-w-sm">
                                No team member data is available for the selected date range.
                                Try adjusting your date filter or check if tasks exist.
                            </p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {sortedEngineers.map((engineer) => {
                                const performance = getPerformanceLevel(
                                    engineer.totalHours
                                );

                                return (
                                    <Card key={engineer.name}>
                                        <CardContent className="p-4">
                                            <div className="flex items-start justify-between mb-3">
                                                <div className="flex items-center space-x-3">
                                                    <Avatar>
                                                        <AvatarFallback className="bg-primary text-primary-foreground tracking-wide flex items-center justify-center">
                                                            {(() => {
                                                                const initials = engineer.name
                                                                    .split(" ")
                                                                    .map((n) => n[0])
                                                                    .join("")
                                                                    .toUpperCase();
                                                                // If initials are 3+ letters, only show first 2
                                                                return initials.length > 2
                                                                    ? initials.slice(0, 2)
                                                                    : initials;
                                                            })()}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <div>
                                                        <h3 className="font-semibold text-sm">
                                                            {engineer.name}
                                                        </h3>
                                                        <Badge
                                                            className={performance.color}
                                                            variant="secondary"
                                                        >
                                                            {performance.level} Activity
                                                        </Badge>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="space-y-2">
                                                <div className="flex items-center justify-between text-sm">
                                                    <span className="flex items-center">
                                                        <Clock className="h-3 w-3 mr-1" />
                                                        Hours
                                                    </span>
                                                    <span className="font-medium">
                                                        {engineer.totalHours.toFixed(1)}h
                                                    </span>
                                                </div>

                                                <div className="flex items-center justify-between text-sm">
                                                    <span className="flex items-center">
                                                        <CheckCircle className="h-3 w-3 mr-1" />
                                                        Tasks
                                                    </span>
                                                    <span className="font-medium">
                                                        {engineer.taskCount}
                                                    </span>
                                                </div>

                                                <div className="flex items-center justify-between text-sm">
                                                    <span className="flex items-center">
                                                        <TrendingUp className="h-3 w-3 mr-1" />
                                                        Story Points
                                                    </span>
                                                    <span className="font-medium">
                                                        {engineer.storyPoints}
                                                    </span>
                                                </div>

                                                <div className="flex items-center justify-between text-sm">
                                                    <span className="flex items-center">
                                                        <TrendingUp className="h-3 w-3 mr-1" />
                                                        Efficiency
                                                    </span>
                                                    <span className="font-medium">
                                                        {engineer.efficiency.toFixed(1)}h/task
                                                    </span>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                );
                            })}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Task Table */}
            <TaskTable data={data.tasks} />
        </div>
    );
}

"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Clock } from "lucide-react";
import { RootState } from "@/app/store";
import { useSelector } from "react-redux";
import {format} from "date-fns";
import {countWeekdaysExcludingHolidays, getGroupedActivity} from "@/lib/utils";

interface TimeDistributionChartProps {
  data: any[];
}

export function TimeDistributionChart({ data }: TimeDistributionChartProps) {
    const { startDate, endDate } = useSelector(
        (state: RootState) => state.dateFilter
    );
    const endDateTimeDistribution = endDate ? endDate : startDate
    const totalDaysFilter = countWeekdaysExcludingHolidays(format(new Date(startDate), "yyyy-MM-dd"), format(new Date(endDateTimeDistribution), "yyyy-MM-dd"));
    const totalHoursFilter = totalDaysFilter * 8;
    // Group by engineer and calculate total hours
    const groupedResult = {};
    data.forEach((entry) => {
        const { name, work_log_comment, work_log_time_spent_hour } = entry;
        const match = work_log_comment.match(/\[([^\]]+)\]/);
        const commentKey = match ? match[1] : work_log_comment.split("\n")[0];
        const activity = getGroupedActivity(entry, commentKey);

    if (!groupedResult[name]) {
      groupedResult[name] = {};
    }

    if (!groupedResult[name][activity]) {
      groupedResult[name][activity] = 0;
    }

    groupedResult[name][activity] += work_log_time_spent_hour;
  });
  const engineerHours = data.reduce((acc, item) => {
    acc[item.name] = (acc[item.name] || 0) + item.work_log_time_spent_hour;
    return acc;
  }, {} as Record<string, number>);

  const sortedEngineers = Object.entries(engineerHours).sort(
    ([, a], [, b]) => (b as number) - (a as number)
  ).filter(([engineer]: [string, unknown]) => !engineer.includes("Bian"));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Time Distribution</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {sortedEngineers.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
              <Clock className="h-8 w-8 text-gray-400 dark:text-gray-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              No Time Data Available
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-sm">
              No time distribution data found for the selected period. Work logs
              might not contain time tracking information.
            </p>
          </div>
        ) : (
          sortedEngineers.map(([engineer, hours]) => {
            const percentageHourWorklog = (hours / totalHoursFilter) * 100;
            return (
              <div key={engineer} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{engineer}</span>
                  <span className="text-sm">
                    {(hours as number).toFixed(1)}h (
                    {percentageHourWorklog.toFixed(1)} %)
                  </span>
                </div>
                <Progress value={percentageHourWorklog} className="h-2" />
                <ul className="list-disc px-4">
                  {Object.entries(groupedResult[engineer])
                    .sort(([, a], [, b]) => (b as number) - (a as number))
                    .map(([activity, hourActivity]) => {
                      const percentage =
                        (hours as number) > 0
                          ? ((hourActivity || 0) / (hours as number)) * 100
                          : 0;
                      return (
                        <li
                          key={activity}
                          className="text-xs text-muted-foreground"
                        >
                          <span className="font-bold text-foreground">
                            {activity}: {percentage.toFixed(1)}%
                          </span>{" "}
                          of total time
                        </li>
                      );
                    })}
                </ul>
              </div>
            );
          })
        )}
      </CardContent>
    </Card>
  );
}

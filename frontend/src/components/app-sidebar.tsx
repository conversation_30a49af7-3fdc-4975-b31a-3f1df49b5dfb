"use client";

import {
  Server,
  LucideTestTubeDiagonal,
  Wrench,
  Rocket,
  Computer,
  NotebookTabsIcon,
  NotebookTextIcon,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import Link from "next/link";
import { SheetClose } from "./ui/sheet";
import { useIsMobile } from "@/hooks/use-mobile";

const roleCategories = [
  {
    title: "Performance Analysis",
    roles: [
      {
        id: "pe",
        name: "Platform Engineer",
        icon: Server,
        active: true,
        href: "/pe/performance-analysis",
      },
      {
        id: "sdet",
        name: "SDET",
        icon: LucideTestTubeDiagonal,
        active: true,
        href: "/sdet/performance-analysis",
      },
      {
        id: "sre",
        name: "SRE",
        icon: Wrench,
        active: true,
        href: "/sre/performance-analysis",
      },
      {
        id: "force",
        name: "Force",
        icon: Rocket,
        active: true,
        href: "/force/performance-analysis",
      },
      {
        id: "engineering",
        name: "Engineering",
        icon: Computer,
        active: true,
        href: "/eng/performance-analysis",
      },
    ],
  },
];

type AppSidebarProps = React.ComponentProps<typeof Sidebar>;

export function AppSidebar(props: AppSidebarProps) {
  const isMobile = useIsMobile();
  return (
    <Sidebar {...props}>
      <SidebarHeader className="flex items-center justify-between pr-2">
        <SidebarMenu>
          <SidebarMenuButton
            asChild
            className="data-[slot=sidebar-menu-button]:!px-1.5"
          >
            <Link href="/">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="icon icon-tabler icons-tabler-outline icon-tabler-brand-unity"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M14 3l6 4v7" />
                <path d="M18 17l-6 4l-6 -4" />
                <path d="M4 14v-7l6 -4" />
                <path d="M4 7l8 5v9" />
                <path d="M20 7l-8 5" />
              </svg>
              <span className="text-base font-semibold">Pentools</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        {roleCategories.map((category) => (
          <SidebarGroup key={category.title}>
            <SidebarGroupLabel>{category.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {category.roles.map((role) => {
                  const Icon = role.icon;
                  return (
                    <SidebarMenuItem key={role.id}>
                      <SidebarMenuButton asChild disabled={!role.active}>
                        {isMobile ? (
                          <SheetClose asChild>
                            <Link
                              href={role.href}
                              className={
                                !role.active
                                  ? "opacity-50 cursor-not-allowed"
                                  : ""
                              }
                            >
                              <Icon />
                              <span>{role.name}</span>
                            </Link>
                          </SheetClose>
                        ) : (
                          <Link
                            href={role.href}
                            className={
                              !role.active
                                ? "opacity-50 cursor-not-allowed"
                                : ""
                            }
                          >
                            <Icon />
                            <span>{role.name}</span>
                          </Link>
                        )}
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
        <SidebarGroup>
          <SidebarGroupLabel>Tools</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link href="/jira-task-maker/frontend">
                    <NotebookTabsIcon />
                    <span>Jira Task Maker FE</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link href="/jira-task-maker/backend">
                    <NotebookTextIcon />
                    <span>Jira Task Maker BE</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}

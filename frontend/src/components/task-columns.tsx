"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowUpDown, ExternalLink } from "lucide-react";

export interface Task {
    assignee: string;
    created: string;
    labels: string[];
    parent_link: string;
    parent_name: string;
    priority: string;
    reporter: string;
    sp: number;
    status: string;
    task_link: string;
    task_name: string;
    time_spent_hours: number;
    time_spent_human: string;
    time_spent_second: number;
    type: string;
    updated: string;
    worklogs: Array<{
        created_at: string;
        description: string;
        name: string;
        time_spent_hour: number;
        time_spent_human: string;
        time_spent_second: number;
        type: string;
        user_id: string;
    }>;
}

const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
        case "highest":
            return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
        case "high":
            return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
        case "medium":
            return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
        case "low":
            return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
        case "lowest":
            return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
        default:
            return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
};

const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case "in progress":
            return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
        case "in testing dev (optional)":
            return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
        case "in testing stg (optional)":
            return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200";
        case "done":
            return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
        default:
            return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
};

export const taskColumns: ColumnDef<Task>[] = [
    {
        accessorKey: "task_name",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Task Name
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        size: 300, // Fixed width for the column
        minSize: 250,
        maxSize: 350,
        cell: ({ row }) => {
            const task = row.original;
            return (
                <div className="space-y-1 max-w-xs">
                    <div
                        className="font-medium truncate"
                        title={task.task_name}
                    >
                        {task.task_name}
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() => window.open(task.task_link, "_blank")}
                        >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            View
                        </Button>
                    </div>
                </div>
            );
        },
    },
    {
        accessorKey: "assignee",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Assignee
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
    },
    {
        accessorKey: "parent_name",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Parent
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        cell: ({ row }) => {
            const parentName = row.getValue("parent_name") as string;
            return parentName ? (
                <div
                    className="truncate max-w-xs"
                    title={parentName}
                >
                    {parentName}
                </div>
            ) : (
                <span className="text-muted-foreground">-</span>
            );
        },
        enableSorting: true,
        enableColumnFilter: true,
    },
    {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => {
            const status = row.getValue("status") as string;
            return (
                <Badge className={getStatusColor(status)} variant="secondary">
                    {status}
                </Badge>
            );
        },
    },
    {
        accessorKey: "priority",
        header: "Priority",
        cell: ({ row }) => {
            const priority = row.getValue("priority") as string;
            return (
                <Badge className={getPriorityColor(priority)} variant="secondary">
                    {priority}
                </Badge>
            );
        },
    },
    {
        accessorKey: "type",
        header: "Type",
        cell: ({ row }) => {
            const type = row.getValue("type") as string;
            return (
                <Badge variant="outline">
                    {type}
                </Badge>
            );
        },
    },
    {
        accessorKey: "sp",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Story Points
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        cell: ({ row }) => {
            const sp = row.getValue("sp") as number;
            return <div className="text-center">{sp}</div>;
        },
    },
    {
        accessorKey: "time_spent_hours",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Time Spent
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        cell: ({ row }) => {
            const human = row.original.time_spent_human;
            return (
                <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">{human}</div>
                </div>
            );
        },
    },
    {
        accessorKey: "labels",
        header: "Labels",
        cell: ({ row }) => {
            const labels = row.getValue("labels") as string[];
            return (
                <div className="flex flex-wrap gap-1">
                    {labels.slice(0, 3).map((label, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                            {label}
                        </Badge>
                    ))}
                    {labels.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                            +{labels.length - 3}
                        </Badge>
                    )}
                </div>
            );
        },
    },
    {
        accessorKey: "created",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Created
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        cell: ({ row }) => {
            const created = row.getValue("created") as string;
            return new Date(created).toLocaleDateString();
        },
    },
    {
        accessorKey: "updated",
        header: ({ column }) => {
            return (
                <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Updated
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        cell: ({ row }) => {
            const updated = row.getValue("updated") as string;
            return new Date(updated).toLocaleDateString();
        },
    },
];

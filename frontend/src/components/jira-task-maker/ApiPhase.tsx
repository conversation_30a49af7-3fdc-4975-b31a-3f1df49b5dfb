import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Trash2, Plus } from "lucide-react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn, useFieldArray } from "react-hook-form";

export interface ApiContract {
  method: string;
  requestPayload: string;
  responsePayload: string;
}

export interface ApiSpec {
  name: string;
  endpointUrl: string;
  contract: ApiContract;
}

type ApiPhaseProps = {
  form: UseFormReturn<any>;
};

const httpMethods = [
  "",
  "GET",
  "POST",
  "PUT",
  "PATCH",
  "DELETE",
  "HEAD",
  "OPTIONS",
];

export default function ApiPhase({ form }: ApiPhaseProps) {
  const { control } = form;
  const { fields, append, remove } = useFieldArray({
    control,
    name: "api",
  });

  return (
    <div className="space-y-6">
      {fields.length > 0 && (
        <div className="space-y-8">
          {fields.map((field, idx) => (
            <div
              key={field.id}
              className="relative border border-border rounded-xl p-4 bg-gradient-to-br from-background to-muted/30 shadow-sm hover:shadow-md transition-all duration-200"
            >
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <h4 className="text-lg font-semibold text-foreground">
                    API Specification {idx + 1}
                  </h4>
                </div>
                {fields.length > 1 && (
                  <Button
                    variant="outline"
                    size="sm"
                    type="button"
                    onClick={() => remove(idx)}
                    className="hover:bg-destructive/10 hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
              <div className="space-y-6">
                <FormField
                  name={`api.${idx}.name`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium">API Name</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="e.g., User Authentication API"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  name={`api.${idx}.endpointUrl`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium">
                        Endpoint URL
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="https://api.example.com/auth"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="border border-border rounded-lg p-4 bg-background">
                  <h5 className="font-medium text-foreground mb-4">
                    API Contract
                  </h5>
                  <div className="space-y-4">
                    <FormField
                      name={`api.${idx}.contract.method`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">
                            HTTP Method
                          </FormLabel>
                          <FormControl>
                            <select
                              {...field}
                              className="w-full px-3 py-2 border border-border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground"
                            >
                              {httpMethods.map((method) => (
                                <option key={method} value={method}>
                                  {method ? method : "Select HTTP method"}
                                </option>
                              ))}
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      name={`api.${idx}.contract.requestPayload`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">
                            Request Payload (JSON)
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              rows={4}
                              placeholder={`{\n  "email": "string",\n  "password": "string"\n}`}
                              className="font-mono text-sm"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      name={`api.${idx}.contract.responsePayload`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-medium">
                            Response Payload (JSON)
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              rows={4}
                              placeholder={`{\n  "token": "string",\n  "user": {\n    "id": "string",\n    "email": "string"\n  }\n}`}
                              className="font-mono text-sm"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      <Button
        variant="outline"
        size="sm"
        type="button"
        onClick={() =>
          append({
            name: "",
            endpointUrl: "",
            contract: { method: "", requestPayload: "", responsePayload: "" },
          })
        }
      >
        <Plus className="w-4 h-4 mr-2" />
        Add API Specification
      </Button>
    </div>
  );
}

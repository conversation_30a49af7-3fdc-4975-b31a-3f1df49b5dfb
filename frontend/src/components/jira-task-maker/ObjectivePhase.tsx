import React, { useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Trash2 } from "lucide-react";
import {
  FormField,
  FormItem,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn, useFieldArray } from "react-hook-form";

type ObjectivePhaseProps = {
  form: UseFormReturn<any>;
  error?: string;
};

export default function ObjectivePhase({ form, error }: ObjectivePhaseProps) {
  const { control } = form;
  const { fields, append, remove } = useFieldArray({
    control,
    name: "objective",
  });
  const inputRefs = useRef<Array<HTMLInputElement | null>>([]);

  const handleAdd = () => {
    append({ value: "" });
    setTimeout(() => {
      const nextInput = inputRefs.current[fields.length];
      if (nextInput) nextInput.focus();
    }, 100);
  };

  const handleRemove = (idx: number) => {
    if (fields.length > 1) {
      remove(idx);
      setTimeout(() => {
        const prevInput = inputRefs.current[idx - 1];
        if (prevInput) prevInput.focus();
      }, 100);
    }
  };

  return (
    <div className="space-y-4">
      {error && <div className="text-red-500 text-xs mb-2">{error}</div>}
      <div className="space-y-3">
        {fields.map((field, idx) => (
          <FormField
            key={field.id}
            name={`objective.${idx}.value`}
            render={({ field }) => (
              <FormItem className="flex items-center gap-2">
                <FormControl>
                  <Input
                    {...field}
                    ref={(el) => {
                      inputRefs.current[idx] = el;
                    }}
                    placeholder={`Objective ${idx + 1}...`}
                    className="flex-1 border border-border rounded-lg focus:border-primary focus:ring-2 focus:ring-primary hover:border-border"
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        handleAdd();
                      } else if (
                        e.key === "Backspace" &&
                        field.value === "" &&
                        fields.length > 1
                      ) {
                        e.preventDefault();
                        handleRemove(idx);
                      }
                    }}
                  />
                </FormControl>
                {fields.length > 1 && (
                  <Button
                    variant="outline"
                    size="sm"
                    type="button"
                    onClick={() => handleRemove(idx)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
                <FormMessage />
              </FormItem>
            )}
          />
        ))}
      </div>
      <Button
        variant="outline"
        size="sm"
        type="button"
        className="mt-3 text-muted-foreground"
        onClick={handleAdd}
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Objective
      </Button>
    </div>
  );
}

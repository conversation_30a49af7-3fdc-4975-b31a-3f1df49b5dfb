import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { eachDayOfInterval, format, getDay, parseISO } from "date-fns";

const holidays: Array<string> = [];
const FrontendPE = [
  {
    id: "557058:170997e2-bc24-4549-ac70-c9dd666bcd0c",
    name: "<PERSON><PERSON>",
  },
  {
    id: "712020:781a66ca-5cf6-40ea-9593-035ba947fd40",
    name: "<PERSON><PERSON><PERSON>",
  },
  {
    id: "712020:3b3b163c-7449-42d9-8722-a8b825e1410d",
    name: "<PERSON><PERSON><PERSON>",
  },
  {
    id: "712020:fdb9158c-f15a-448d-aa56-61d177b2f510",
    name: "<PERSON>mba<PERSON>",
  },
];
const BackendPE = [
  {
    id: "712020:7727b678-7a39-4627-a7ca-62d9a0da12e1",
    name: "<PERSON><PERSON>",
  },
  {
    id: "712020:b6ff5117-0224-4899-a5fc-7499e7a23188",
    name: "<PERSON>",
  },
  {
    id: "712020:01e1fed4-1e4f-4481-aeb2-c096155e1f7b",
    name: "Bayu",
  },
  {
    id: "712020:e6324922-858c-4a85-a3a2-d6fe359325f7",
    name: "Hasan",
  },
];

export interface PlatformEngineerLoadRecommendation {
  isOverload: boolean;
  tasksRecommendation: Array<any>;
  handoverRecommendation: Array<any>;
  remainingCapacity: number;
}

export interface EngineerMetrics {
  totalHoursBreakdown: number;
  totalStoryPointsBreakdown: number;
  breakdownSla: number;
  totalBacklogEpic: number;
  totalBacklogStoryPoint: number;
  totalMondayWednesdayEpic: number;
  totalMondayWednesdayStoryPoint: number;
  totalThursdayFridayEpic: number;
  totalThursdayFridayStoryPoint: number;
  totalBacklogEpicDone: number;
  totalBacklogStoryPointDone: number;
  totalMondayWednesdayEpicDone: number;
  totalMondayWednesdayStoryPointDone: number;
  totalThursdayFridayEpicDone: number;
  totalThursdayFridayStoryPointDone: number;
  name: string;
  epic: any;
  loadRecommendations: PlatformEngineerLoadRecommendation;
  userID?: string;
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Get color classes for different Jira issue types.
 *
 * @param type The type of the Jira issue (e.g., "Epic", "Task", "Sub-task", "Story").
 * @returns Tailwind CSS classes for the given issue type.
 */
export const getTypeColor = (type: string) => {
  switch (type) {
    case "Epic":
      return "bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-200";
    case "Task":
      return "bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-200";
    case "Sub-task":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    case "Story":
      return "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
};

/**
 * Get color classes for activity badges based on activity type or comment content.
 * Returns Tailwind classes for both light and dark mode.
 * @param input The activity string or comment to get colors for
 */
export function getActivityColor(input: string): string {
  const text = input.toUpperCase();

  // Check for activity patterns in the input (works for both direct activity names and comments)
  if (text.includes("DONE"))
    return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
  if (
    text.includes("ANALYSIS TECHNICAL") ||
    text.includes("ANALYSIS DB") ||
    text.includes("ANALYSIS API") ||
    text.includes("ANALYSIS UI & UX")
  )
    return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
  if (text.includes("PAIRING"))
    return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
  if (text.includes("REVIEW") || text.includes("CODE"))
    return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
  if (text.includes("WORKING"))
    return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
  if (text.includes("DISCUSSION"))
    return "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200";
  if (text.includes("PROJECT SA"))
    return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200";
  if (text.includes("REVISIT"))
    return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200";
  if (text.includes("ISSUE"))
    return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
  if (text.includes("MEETING"))
    return "bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200";
  if (text.includes("ON LEAVE"))
    return "bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-200";
  if (text.includes("OTHERS"))
    return "bg-neutral-100 text-neutral-800 dark:bg-neutral-900 dark:text-neutral-200";

  return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
}

export const getEpicStatusColor = (status: string) => {
  switch (status) {
    case "Done":
      return "bg-emerald-100 dark:bg-emerald-800 text-emerald-800 dark:text-emerald-100";
    case "In Progress":
      return "bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100";
    case "Backlog":
      return "bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-100";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const getLoadColor = (isOverload: boolean) => {
  if (isOverload) {
    return "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200";
  }
  return "bg-emerald-100 dark:bg-emerald-900 text-emerald-800 dark:text-emerald-200";
};

export const getEngTaskColor = (status: string) => {
  const doneStatuses = new Set(["Done", "Ready to Test"]);
  const inProgressStatuses = new Set(["In Progress", "In QA"]);

  if (doneStatuses.has(status)) {
    return "bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100"; // ✅ similar to Jira's green
  }

  if (inProgressStatuses.has(status)) {
    return "bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100"; // 🟦 similar to Jira's blue
  }

  return "bg-gray-100 text-gray-800"; // ⚪ default
};

/**
 * Format a Jira-style time spent string (e.g., "2h0m0s") to only show nonzero units (e.g., "2h", "2h 30m", "2h 30m 10s").
 * Converts 8 hours to 1 day for better readability.
 * @param timeStr string like "2h0m0s", "0h30m0s", "0h0m10s", etc.
 */
export function formatTimeSpent(timeStr: string): string {
  if (!timeStr) return "";
  if (timeStr === "0m") return "-";

  // Match hours, minutes, seconds with optional spaces (e.g., "2h", "30m", "10s", "2h 30m", "2h0m0s")
  const match = timeStr.match(/^(?:(\d+)h)?\s*(?:(\d+)m)?\s*(?:(\d+)s)?$/);
  if (!match || (!match[1] && !match[2] && !match[3])) return timeStr;

  const h = parseInt(match[1] ?? "0");
  const m = match[2] ?? undefined;
  const s = match[3] ?? undefined;

  const parts: string[] = [];

  // Convert hours to days (8h = 1d)
  if (h >= 8) {
    const days = Math.floor(h / 8);
    const remainingHours = h % 8;
    parts.push(`${days}d`);
    if (remainingHours > 0) parts.push(`${remainingHours}h`);
  } else if (h > 0) {
    parts.push(`${h}h`);
  }

  if (m && m !== "0") parts.push(`${m}m`);
  if (s && s !== "0") parts.push(`${s}s`);

  // If all are zero, show "-" instead of "0s"
  return parts.length > 0 ? parts.join(" ") : "-";
}

export function getCalculateDoneStoryPoints(dataEpics, filterDate) {
  return dataEpics.reduce((sumEpic, epic) => {
    return (
      sumEpic +
      epic.issues
        .filter((issue) => {
          const doneLog = issue.worklogs.find((log) =>
            log.type.toLowerCase().includes("done")
          );
          if (doneLog) {
            return (
              new Date(doneLog.created_at).setHours(0, 0, 0, 0) >=
                new Date(filterDate.start_date).setHours(0, 0, 0, 0) &&
              new Date(doneLog.created_at).setHours(0, 0, 0, 0) <=
                new Date(filterDate.end_date).setHours(0, 0, 0, 0) &&
              issue.status === "Done"
            );
          }
        })
        .reduce((sumIssue, issue) => {
          return sumIssue + issue.sp;
        }, 0)
    );
  }, 0);
}
export function getTotalBreakdownHours(dataEpics, filterDate) {
  const engineerIssues = dataEpics.flatMap((epic) => epic.issues);
  const breakdownHours = dataEpics.reduce((sumEpic, epic) => {
    return (
      sumEpic +
      epic.worklogs
        .filter(
          (worklog) =>
            new Date(worklog.created_at).setHours(0, 0, 0, 0) >=
              new Date(filterDate.start_date).setHours(0, 0, 0, 0) &&
            new Date(worklog.created_at).setHours(0, 0, 0, 0) <=
              new Date(filterDate.end_date).setHours(0, 0, 0, 0)
        )
        .reduce((sumWorklog, worklog) => {
          return sumWorklog + worklog.time_spent_hour;
        }, 0)
    );
  }, 0);
  return (
    breakdownHours +
    engineerIssues
      .filter((issue) => {
        const doneLog = issue.worklogs.find((log) =>
          log.type.toLowerCase().includes("done")
        );
        if (doneLog) {
          return (
            new Date(doneLog.created_at).setHours(0, 0, 0, 0) >=
              new Date(filterDate.start_date).setHours(0, 0, 0, 0) &&
            new Date(doneLog.created_at).setHours(0, 0, 0, 0) <=
              new Date(filterDate.end_date).setHours(0, 0, 0, 0) &&
            issue.status === "Done"
          );
        }
      })
      .reduce((sum, issue) => {
        return (
          sum +
          issue.worklogs
            .filter(
              (worklog) =>
                getGroupedActivity(worklog, worklog.type) === "BREAKDOWN"
            )
            .reduce((sumWorklog, worklog) => {
              return sumWorklog + worklog.time_spent_hour;
            }, 0)
        );
      }, 0)
  );
}
export function getDoneBacklogEpics(dataEpics, filterDate) {
  return dataEpics
    .filter((epic) => {
      return (
        new Date(epic.grooming_date).setHours(0, 0, 0, 0) <
        new Date(filterDate.start_date).setHours(0, 0, 0, 0)
      );
    })
    .filter((epic) => {
      return epic.issues.every((issue) => {
        return issue.status === "Done";
      });
    });
}
export function getDoneWeeklyEpics(dataEpics) {
  return dataEpics.filter((epic) => {
    return epic.issues.every((issue) => {
      return issue.status === "Done";
    });
  });
}
export function getNotDoneWeeklyEpics(dataEpics) {
  return dataEpics.filter((epic) => {
    return epic.issues.some((issue) => {
      return issue.status !== "Done";
    });
  });
}
export function getThursdayFridayEpics(dataEpics, filterDate) {
  const end = new Date(filterDate.end_date);
  const endDateMinus1days = end.setDate(end.getDate() - 1);
  return dataEpics.filter((epic) => {
    return (
      new Date(epic.grooming_date).setHours(0, 0, 0, 0) <=
        new Date(filterDate.end_date).setHours(0, 0, 0, 0) &&
      new Date(epic.grooming_date).setHours(0, 0, 0, 0) >=
        new Date(endDateMinus1days).setHours(0, 0, 0, 0)
    );
  });
}
export function getThursdayFridayStoryPoints(dataEpics, filterDate) {
  const epicAddingThursdayFriday = getThursdayFridayEpics(
    dataEpics,
    filterDate
  );
  if (epicAddingThursdayFriday.length === 0) return 0;
  return epicAddingThursdayFriday.reduce((sumEpic, epic) => {
    return (
      sumEpic +
      epic.issues.reduce((sumIssue, issue) => {
        return sumIssue + issue.sp;
      }, 0)
    );
  }, 0);
}
export function getMondayWednesdayEpics(dataEpics, filterDate) {
  return dataEpics.filter((epic) => {
    const start = new Date(filterDate.start_date);
    const endDate2days = start.setDate(start.getDate() + 2);
    return (
      new Date(epic.grooming_date).setHours(0, 0, 0, 0) >=
        new Date(filterDate.start_date).setHours(0, 0, 0, 0) &&
      new Date(epic.grooming_date).setHours(0, 0, 0, 0) <=
        new Date(endDate2days).setHours(0, 0, 0, 0)
    );
  });
}
export function getMondayWednesdayStoryPoints(dataEpics, filterDate) {
  const epicAddingMondayWednesday = getMondayWednesdayEpics(
    dataEpics,
    filterDate
  );
  if (epicAddingMondayWednesday.length === 0) return 0;
  return epicAddingMondayWednesday.reduce((sumEpic, epic) => {
    return (
      sumEpic +
      epic.issues.reduce((sumIssue, issue) => {
        return sumIssue + issue.sp;
      }, 0)
    );
  }, 0);
}
export function getBacklogEpics(dataEpics, filterDate) {
  return dataEpics
    .filter((epic) => {
      return !epic.epic_name.includes("Worklog");
    })
    .filter((epic) => {
      return (
        new Date(epic.grooming_date).setHours(0, 0, 0, 0) <
        new Date(filterDate.start_date).setHours(0, 0, 0, 0)
      );
    });
}
export function getTotalStoryPointsFromBacklog(dataEpics, filterDate) {
  const backlogEpics = getBacklogEpics(dataEpics, filterDate);
  if (backlogEpics.length === 0) return 0;
  return backlogEpics.reduce((sumEpic, epic) => {
    return (
      sumEpic +
      epic.issues
        .filter((issue) => {
          const doneLog = issue.worklogs.find((log) =>
            log.type.toLowerCase().includes("done")
          );
          if (doneLog) {
            return (
              new Date(doneLog.created_at).setHours(0, 0, 0, 0) >=
              new Date(filterDate.start_date).setHours(0, 0, 0, 0)
            );
          }
          return true;
        })
        .reduce((sumIssue, issue) => {
          return sumIssue + issue.sp;
        }, 0)
    );
  }, 0);
}
export function countWeekdaysExcludingHolidays(startDateStr, endDateStr) {
  let trueEndDateStr = endDateStr;
  if (
    new Date().setHours(0, 0, 0, 0) <= new Date(endDateStr).setHours(0, 0, 0, 0)
  ) {
    trueEndDateStr = format(new Date(), "yyyy-MM-dd");
  }
  const days = eachDayOfInterval({
    start: parseISO(startDateStr),
    end: parseISO(trueEndDateStr),
  });

  const weekdays = days.filter((date) => {
    const day = getDay(date); // 0 = Minggu, 6 = Sabtu
    const formatted = format(date, "yyyy-MM-dd");
    const isWeekday = day >= 1 && day <= 5;
    const isHoliday = holidays.includes(formatted);
    return isWeekday && !isHoliday;
  });

  return weekdays.length;
}
export function calculateRecommendation(
  startDate,
  endDate,
  engineer: EngineerMetrics
): PlatformEngineerLoadRecommendation {
  const slaBreakdown = Math.max(engineer.breakdownSla, 2);
  const endDateTimeDistribution = endDate ? endDate : startDate;
  const totalDaysFilter = countWeekdaysExcludingHolidays(
    new Date(endDateTimeDistribution).toISOString(),
    new Date().toISOString()
  );
  const totalHoursFilter = (totalDaysFilter - 1) * 6;
  const maxCapacity = slaBreakdown * totalHoursFilter;
  let remainingCapacity = maxCapacity;
  const remainingBacklog =
    engineer.totalBacklogStoryPoint +
    engineer.totalMondayWednesdayStoryPoint +
    engineer.totalThursdayFridayStoryPoint -
    engineer.totalStoryPointsBreakdown;
  const isOverload = remainingBacklog > maxCapacity;
  const engineerIssuesInEpic = engineer.epic.flatMap((epic) => epic.issues);
  const taskRecommendation: Array<any> = [];
  const handoverRecommendation: Array<any> = [];
  engineerIssuesInEpic
    .filter((issue) => issue.status != "Done")
    .sort((a, b) => a.status - b.status)
    .forEach((issue) => {
      if (remainingCapacity - issue.sp >= -2) {
        taskRecommendation.push(issue);
      } else {
        handoverRecommendation.push(issue);
      }
      remainingCapacity = remainingCapacity - issue.sp;
    });
  return {
    isOverload: isOverload,
    tasksRecommendation: taskRecommendation,
    handoverRecommendation: handoverRecommendation,
    remainingCapacity: remainingCapacity,
  };
}
export function getRecommendationHandover(
  userId: string,
  engineers: Array<EngineerMetrics>
) {
  let availableEngineers: any[];
  const isFrontend = FrontendPE.some((eng) => eng.id === userId);
  if (isFrontend) {
    availableEngineers = engineers.filter((engineer) => {
      return (
        !engineer.loadRecommendations.isOverload &&
        FrontendPE.some((user) => user.id === engineer.userID)
      );
    });
  } else {
    availableEngineers = engineers.filter((engineer) => {
      return (
        !engineer.loadRecommendations.isOverload &&
        BackendPE.some((user) => user.id === engineer.userID)
      );
    });
  }
  return availableEngineers.map((user) => user.name).join(", ");
}

export function getGroupedActivity(item, commentKey: string) {
  if (
    commentKey.toUpperCase().includes("PROJECT SA") ||
    commentKey.includes("CODING") ||
    commentKey.includes("CODE") ||
    commentKey.includes("RESEARCH")
  ) {
    return "PROJECT SA";
  }
  if (
    commentKey === "DETAILING" ||
    commentKey.includes("DONE") ||
    commentKey.includes("BREAKDOWN") ||
    commentKey.includes("ANALY") ||
    commentKey.includes("READ") ||
    commentKey.includes("CHECK")
  ) {
    return "BREAKDOWN";
  }
  if (commentKey.toUpperCase().includes("GROOMING")) {
    return "GROOMING";
  }
  if (commentKey.includes("MEETING") || commentKey.includes("DISCUSSION")) {
    return "MEETING";
  }
  if (
    commentKey.includes("PAIRING") ||
    commentKey.includes("REVIEW") ||
    commentKey.includes("REVISIT") ||
    commentKey.includes("SUPPORT") ||
    commentKey.includes("MATRIX")
  ) {
    return "OPERATION";
  }
  if (commentKey.toUpperCase().includes("OTHER")) {
    return "OTHERS";
  }
  return commentKey;
}

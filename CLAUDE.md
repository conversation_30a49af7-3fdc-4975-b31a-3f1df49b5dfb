# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (Go)

- `make run`: Generate Swagger docs and run the Go server
- `make generate`: Generate Swagger documentation from code annotations
- `make test-race`: Run tests with race condition detection using `GOMAXPROCS=4 go test -v -race -run=TestEngSuite ./internal/usecase/eng`
- `go run ./cmd/http/main.go`: Run the HTTP server directly
- Tests are located in `internal/usecase/eng/*_test.go`

### Frontend (Next.js)

- `make fe-install`: Install frontend dependencies using pnpm
- `make fe-run`: Run Next.js development server with Turbopack (`pnpm run dev`)
- `make fe-build`: Build frontend and copy to `cmd/http/dist` for embedding
- `make fe-lint`: Run ESLint with auto-fix (`pnpm run lint`)
- `make run-all`: Build frontend and run full application

### Full Stack

- `make run-all`: Complete build and run pipeline (frontend build + backend run)

## Architecture Overview

### Backend Structure

This is a Go web application using Echo framework with clean architecture:

- **cmd/http/main.go**: Main entry point with embedded frontend files and API routing
- **internal/delivery/**: HTTP handlers organized by domain (eng, jira, sdet, sa)
- **internal/usecase/**: Business logic layer with domain-specific use cases
- **internal/repository/**: Data access layer (Jira API, PostgreSQL)
- **internal/dto/**: Data transfer objects for API communication
- **pkg/**: Shared utilities (config, database, logging, telemetry)

### Frontend Structure

Next.js 15 application with App Router:

- **src/app/**: App Router pages and layouts
- **src/components/**: Reusable React components with shadcn/ui
- **src/types/**: TypeScript type definitions
- **Redux Toolkit** + **Redux Persist** for state management
- **TanStack Query** for server state and API calls
- **Tailwind CSS** for styling

### Key Integrations

- **Jira API**: Primary data source for engineering metrics and reporting
- **PostgreSQL**: Database for SDET-specific data
- **OpenTelemetry**: Observability with Prometheus metrics
- **Swagger**: API documentation auto-generated from code

### Domain Areas

- **eng/**: Engineering team performance analytics (bugs, epics, leaderboards)
- **sdet/**: Software Development Engineer in Test metrics and reporting
- **jira/**: Direct Jira integration and reporting capabilities
- **sa/**: System Administrator tools and metrics

### Configuration

- Configuration via TOML files in `config/` directory
- Environment-specific configs supported (local, production, etc.)
- Database connections, Jira credentials, and app settings configurable

### Testing

- Go unit tests with testify framework
- Test suites in `internal/usecase/eng/` with comprehensive coverage
- Race condition testing available via makefile

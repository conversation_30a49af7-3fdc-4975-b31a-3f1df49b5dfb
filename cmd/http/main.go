package main

//go:generate go run github.com/swaggo/swag/cmd/swag init -o ../../docs/ -g cmd/http/main.go -d ../../
import (
	"context"
	"embed"
	"io/fs"
	"mime"
	h "net/http"
	"os"
	"path/filepath"

	_ "github.com/Lionparcel/pentools/docs"
	engHTTP "github.com/Lionparcel/pentools/internal/delivery/eng/http"
	jiraHTTP "github.com/Lionparcel/pentools/internal/delivery/jira/http"
	saHTTP "github.com/Lionparcel/pentools/internal/delivery/sa/http"
	sdetHTTP "github.com/Lionparcel/pentools/internal/delivery/sdet/http"
	"github.com/Lionparcel/pentools/internal/repository/jira"
	"github.com/Lionparcel/pentools/internal/repository/sdet"
	engUsecase "github.com/Lionparcel/pentools/internal/usecase/eng"
	jiraUsecase "github.com/Lionparcel/pentools/internal/usecase/jira"
	saUsecase "github.com/Lionparcel/pentools/internal/usecase/sa"
	sdetUsecase "github.com/Lionparcel/pentools/internal/usecase/sdet"
	"github.com/Lionparcel/pentools/pkg/config"
	"github.com/Lionparcel/pentools/pkg/database/postgresql"
	"github.com/Lionparcel/pentools/pkg/log"
	"github.com/Lionparcel/pentools/pkg/middleware"
	"github.com/Lionparcel/pentools/pkg/telemetry"
	"github.com/labstack/echo/v4"
	md "github.com/labstack/echo/v4/middleware"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	echoSwagger "github.com/swaggo/echo-swagger"
	"go.opentelemetry.io/contrib/instrumentation/github.com/labstack/echo/otelecho"
)

//go:embed dist/*
var distFS embed.FS

// @title Platform Engineering Tools
// @version 1.0
// @description Collection of Platform Engineering Tools
func main() {
	cfg, err := config.New("./config/app.toml")
	if err != nil {
		panic(err)
	}
	err = log.New(cfg)
	if err != nil {
		panic(err)
	}
	telemetryShutdown, err := telemetry.New(context.Background(), cfg)
	if err != nil {
		log.Fatal(context.Background(), "error while initial telemetry", err)
	}
	defer telemetryShutdown(context.Background())
	sdetDB, err := postgresql.New(cfg)
	if err != nil && cfg.App.Env == "production" {
		panic(err)
	}
	e := echo.New()
	setupEcho(cfg, e, sdetDB)
	e.Logger.Fatal(e.Start(":" + cfg.App.Port))
}

func setupEcho(cfg *config.Config, e *echo.Echo, sdetDB *postgresql.Connections) {
	e.GET("/swagger/*", echoSwagger.WrapHandler)
	useOS := len(os.Args) > 1 && os.Args[1] == "live"
	fsys := getFileSystem(useOS)

	e.GET("/*", func(c echo.Context) error {
		path := c.Param("*")
		if path == "" || path == "/" {
			path = "index.html"
		}

		// First try to serve .html file if path doesn't have .html extension
		if !hasHTMLExt(path) {
			htmlPath := path + ".html"
			f, err := fsys.Open(htmlPath)
			if err == nil {
				defer f.Close()
				return c.Stream(h.StatusOK, "text/html", f)
			}
		}

		// Then try the exact path
		f, err := fsys.Open(path)
		if err == nil {
			defer f.Close()
			contentType := detectContentType(path)
			// Force HTML content type for files without extension that should be HTML
			if contentType == "application/octet-stream" && (path == "index.html" || hasHTMLExt(path)) {
				contentType = "text/html"
			}
			return c.Stream(h.StatusOK, contentType, f)
		}

		// Fallback to index.html for SPA routing
		f3, err3 := fsys.Open("index.html")
		if err3 == nil {
			defer f3.Close()
			return c.Stream(h.StatusOK, "text/html", f3)
		}
		return c.NoContent(h.StatusNotFound)
	})
	e.GET("/metric", func(c echo.Context) error {
		promhttp.Handler().ServeHTTP(c.Response(), c.Request())
		return nil
	})
	e.Use(md.Recover())
	e.Use(otelecho.Middleware(cfg.App.Name))
	e.Use(middleware.Metric)
	e.Use(middleware.RequestID)
	e.Use(middleware.Logger)
	if cfg.App.Env != "production" {
		e.Use(md.CORS())
	}

	jira := jira.NewAPI(cfg)
	jiraUsecase := jiraUsecase.New(cfg, jira)
	jiraHTTP.New(cfg, e, jiraUsecase)

	sdetDatabase := sdet.NewDatabase(cfg, sdetDB)
	sdetUsecase := sdetUsecase.New(cfg, sdetDatabase)
	sdetHTTP.New(cfg, e, sdetUsecase)

	saUsecase := saUsecase.New(cfg, jira)
	saHTTP.New(cfg, e, saUsecase)

	engJiraService := engUsecase.NewJiraService(jira)
	engUC := engUsecase.New(engJiraService)
	engHTTP.New(cfg, e, engUC)

}

func hasHTMLExt(path string) bool {
	n := len(path)
	return n > 5 && path[n-5:] == ".html"
}

func detectContentType(path string) string {
	ext := filepath.Ext(path)
	if ext != "" {
		if ctype := mime.TypeByExtension(ext); ctype != "" {
			return ctype
		}
	}
	// fallback for .html and unknown
	if hasHTMLExt(path) || path == "index.html" {
		return "text/html"
	}
	return "application/octet-stream"
}

func getFileSystem(useOS bool) h.FileSystem {
	if useOS {
		log.Info(context.Background(), "using live mode")
		return h.FS(os.DirFS("dist"))
	}

	log.Info(context.Background(), "using embed mode")
	fsys, err := fs.Sub(distFS, "dist")
	if err != nil {
		panic(err)
	}

	return h.FS(fsys)
}

package config

import (
	"github.com/spf13/viper"
)

type Config struct {
	App  App  `mapstructure:"app"`
	Jira Jira `mapstructure:"jira"`
	SDET SDET `mapstructure:"sdet"`
}
type App struct {
	Name string `mapstructure:"name"`
	Port string `mapstructure:"port"`
	Env  string `mapstructure:"env"`
}
type Jira struct {
	SecretKey              string `mapstructure:"secret_key"`
	DefaultJQLGetReporting string `mapstructure:"default_jql_get_reporting"`
	DefaultField           string `mapstructure:"default_field"`
}
type Database struct {
	Host                       string `mapstructure:"host"`
	User                       string `mapstructure:"user"`
	Password                   string `mapstructure:"password"`
	Database                   string `mapstructure:"database"`
	Port                       int    `mapstructure:"port"`
	MaxIdleConnection          int    `mapstructure:"max_idle_connection"`
	MaxOpenConnection          int    `mapstructure:"max_open_connection"`
	ConnectionLifeTimeInSecond int    `mapstructure:"connection_life_time_in_second"`
}
type SDET struct {
	DatabaseMaster Database `mapstructure:"database_master"`
	DatabaseSlave  Database `mapstructure:"database_slave"`
}

func New(configFile string) (*Config, error) {
	cfg := &Config{}
	viper.SetConfigFile(configFile)
	err := viper.ReadInConfig()
	if err != nil {
		return nil, err
	}
	return cfg, viper.Unmarshal(&cfg)
}

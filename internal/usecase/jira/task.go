package jira

import (
	"context"
	"strings"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/timex"
)

func (u *Usecase) GetTasks(ctx context.Context, req dto.GetTasksRequest) (*dto.GetTasksResponse, error) {
	fields := "key,summary,issuetype,status,priority,assignee,reporter,created,updated,labels,parent,description,timetracking,worklog,customfield_10016,customfield_10024,customfield_10080,customfield_10101,creator,customfield_10087"

	// Search for tasks
	taskIssues, err := u.jira.SearchIssue(ctx, model.SearchIssueRequest{
		JQL:    req.JQL,
		Fields: fields,
		Expand: "changelog",
	})
	if err != nil {
		return nil, err
	}

	tasks := make([]dto.JiraTask, 0, len(taskIssues.Issues))

	for _, issue := range taskIssues.Issues {
		task := dto.JiraTask{
			TaskName:   issue.Fields.Summary,
			TaskLink:   issue.TicketLink(),
			Type:       issue.Fields.Issuetype.Name,
			Status:     issue.Fields.Status.Name,
			Priority:   issue.Fields.Priority.Name,
			Assignee:   issue.Fields.Assignee.DisplayName,
			Reporter:   issue.Fields.Reporter.DisplayName,
			Created:    issue.Fields.Created,
			Updated:    issue.Fields.Updated,
			SP:         issue.Fields.SP(),
			Labels:     issue.Fields.Labels,
			ParentName: issue.Fields.Parentx().Fields.Summary,
			ParentLink: issue.Fields.Parentx().TicketLink(),
		}
		worklogs := processTaskWorklogs(issue.Fields.Worklog.Worklogs)
		task.TimeSpentSecond = issue.Fields.Worklog.BreakDownDurationSecond("", time.Now().Add(100*30*12*24*time.Hour))
		if len(worklogs) == 0 {
			worklogs = dto.NewJiraWorklogsFromChangelog(issue.Changelog)
			for i, wl := range worklogs {
				worklogs[i].UserID = issue.Fields.Reporter.AccountID
				worklogs[i].Name = issue.Fields.Reporter.DisplayName
				if wl.Type == "HOLD" {
					continue
				}
				task.TimeSpentSecond += wl.TimeSpentSecond
			}
		}
		task.TimeSpentHours = (time.Duration(task.TimeSpentSecond) * time.Second).Hours()
		task.TimeSpentHuman = timex.FormatDurationHuman(task.TimeSpentSecond)
		task.Worklogs = worklogs

		tasks = append(tasks, task)
	}

	return &dto.GetTasksResponse{
		Tasks: tasks,
	}, nil
}

// processTaskWorklogs filters worklogs based on user IDs and returns total seconds and processed worklogs
func processTaskWorklogs(worklogs []model.WorklogElement) []dto.Worklog {
	processedWorklogs := []dto.Worklog{}

	for _, wl := range worklogs {
		comment := wl.FormattedComment()
		i := strings.Index(comment, "]")
		var typeStr, description string
		if i > 0 && strings.HasPrefix(comment, "[") {
			typeStr = strings.TrimPrefix(strings.TrimSuffix(comment[:i+1], "]"), "[")
			description = comment[i+1:]
		} else {
			description = comment
		}

		processedWorklogs = append(processedWorklogs, dto.Worklog{
			Name:            wl.Author.DisplayName,
			UserID:          wl.Author.AccountID,
			Type:            typeStr,
			Description:     description,
			TimeSpentHuman:  wl.TimeSpent,
			TimeSpentHour:   (time.Duration(wl.TimeSpentSeconds) * time.Second).Hours(),
			TimeSpentSecond: wl.TimeSpentSeconds,
			CreatedAt:       wl.Started,
		})
	}

	return processedWorklogs
}

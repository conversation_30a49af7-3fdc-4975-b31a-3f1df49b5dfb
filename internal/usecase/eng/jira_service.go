package eng

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/patrickmn/go-cache"
	"golang.org/x/sync/errgroup"
)

const chunkSize = 60

// jira interface abstracts JIRA API interactions required by the service.
type jira interface {
	SearchIssue(ctx context.Context, req model.SearchIssueRequest) (*model.SearchIssueResponse, error)
	GroupMember(ctx context.Context, req model.GroupMemberRequest) (*[]model.Author, error)
}

// EngJiraService wraps a JIRA client to provide domain-specific methods for engineering use cases.
type engJiraService struct {
	jira  jira
	cache *cache.Cache
}

type EngJiraService interface {
	GetEpicIssue(ctx context.Context, startDate, endDate string, isActiveSprint bool, jqlConfig JqlConfig) (*[]model.Issue, error)
	GetRelatedBugsByTasks(
		ctx context.Context,
		tasks []model.Issue,
		jqlConfig JqlConfig,
	) (*[]model.Issue, error)
	GetEngineer(ctx context.Context, includeInactiveUsers bool) (*[]model.Author, error)
	GetQa(ctx context.Context, includeInactiveUsers bool) (*[]model.Author, error)
	GetChildTaskIssues(ctx context.Context, parents []string, jqlConfig JqlConfig) (*[]model.Issue, error)
	GetProductionBug(ctx context.Context, startDate, endDate string, jqlConfig JqlConfig) (*[]model.Issue, error)
	GetTaskIssuesByAssignee(ctx context.Context, startDate, endDate string, assignees []string, jqlConfig JqlConfig) (*[]model.Issue, error)
	GetDevBugByDevAssignee(ctx context.Context, startDate, endDate string, assignees []string, jqlConfig JqlConfig) (*[]model.Issue, error)
}

// NewJiraService constructs a new EngJiraService.
func NewJiraService(jira jira) EngJiraService {
	c := cache.New(5*time.Minute, 1*time.Minute)
	return &engJiraService{
		jira:  jira,
		cache: c,
	}
}

// GetEpicIssue retrieves epics active within the provided date range.
func (js *engJiraService) GetEpicIssue(ctx context.Context, startDate, endDate string, isActiveEpic bool, jqlConfig JqlConfig) (*[]model.Issue, error) {
	jql := fmt.Sprintf(`
		"Main Project[Checkboxes]" = Yes AND 
		"Expected Start Date[Date]" <= %s AND 
		"Expected End Date[Date]" >= %s`,
		endDate, startDate,
	)
	if isActiveEpic {
		jqlConfig.AddFields([]JiraFieldOption{FixVersion})

	}
	issues, err := js.jira.SearchIssue(ctx, model.SearchIssueRequest{
		JQL:    jql,
		Fields: jqlConfig.BuildField(),
		Expand: jqlConfig.BuildExpand(),
	})
	if err != nil {
		log.Println("GetEpicIssue error")
		return nil, fmt.Errorf("getEpicIssue error: %w", err)
	}

	if isActiveEpic {
		var activeIssues []model.Issue
		for _, issue := range issues.Issues {
			if len(issue.Fields.FixVersion) == 0 {
				activeIssues = append(activeIssues, issue)
			}
		}
		issues.Issues = activeIssues
	}
	return &issues.Issues, nil
}

// GetTaskIssuesByAssignee retrieves tasks within a date range, optionally filtered by assignees.
// If assignees are provided, they are chunked to avoid exceeding JQL length limits.
func (js *engJiraService) GetTaskIssuesByAssignee(ctx context.Context, startDate, endDate string, assignees []string, jqlConfig JqlConfig) (*[]model.Issue, error) {
	baseJQL := fmt.Sprintf(`
		"Main Project[Checkboxes]" = No AND
		"Expected Start Date[Date]" <= %s AND 
		"Expected End Date[Date]" >= %s`, endDate, startDate)

	var (
		allIssuesMu sync.Mutex
		allIssues   []model.Issue
	)

	g, gctx := errgroup.WithContext(ctx)

	for i := 0; i < len(assignees); i += chunkSize {
		start := i
		end := min(i+chunkSize, len(assignees))
		chunk := assignees[start:end]

		startCopy, endCopy := start, end
		chunkCopy := append([]string(nil), chunk...)

		g.Go(func() error {
			jql := fmt.Sprintf(`assignee IN (%s) AND %s`, strings.Join(chunkCopy, ", "), baseJQL)

			resp, err := js.jira.SearchIssue(gctx, model.SearchIssueRequest{
				JQL:    jql,
				Expand: jqlConfig.BuildExpand(),
				Fields: jqlConfig.BuildField(),
			})
			if err != nil {
				log.Println("GetTaskIssuesByAssignee error")
				return fmt.Errorf("search tasks failed (chunk %d-%d): %w", startCopy, endCopy, err)
			}

			allIssuesMu.Lock()
			allIssues = append(allIssues, resp.Issues...)
			allIssuesMu.Unlock()

			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}

	return &allIssues, nil
}

func (js *engJiraService) GetDevBugByDevAssignee(ctx context.Context, startDate, endDate string, assignees []string, jqlConfig JqlConfig) (*[]model.Issue, error) {
	baseJQL := fmt.Sprintf(`
		issuetype = Bug and 
		status != Backlog and
		"Environment Bug[Dropdown]" IN (Development, Staging) and
		"Accident Bug[Dropdown]" = "Bug in Test Case" and
		created < %s and created >= %s`, endDate, startDate)

	var (
		allIssuesMu sync.Mutex
		allIssues   []model.Issue
	)

	g, gctx := errgroup.WithContext(ctx)

	if len(assignees) > 0 {
		for i := 0; i < len(assignees); i += chunkSize {
			start := i
			end := min(i+chunkSize, len(assignees))
			chunk := assignees[start:end]

			startCopy, endCopy := start, end
			chunkCopy := append([]string(nil), chunk...)

			g.Go(func() error {
				jql := fmt.Sprintf(`"Dev Assignee[User Picker (single user)]" IN (%s) AND %s`, strings.Join(chunkCopy, ", "), baseJQL)

				resp, err := js.jira.SearchIssue(gctx, model.SearchIssueRequest{
					JQL:    jql,
					Expand: jqlConfig.BuildExpand(),
					Fields: jqlConfig.BuildField(),
				})
				if err != nil {
					log.Println("GetDevBugByDevAssignee error")
					return fmt.Errorf("search tasks failed (chunk %d-%d): %w", startCopy, endCopy, err)
				}

				allIssuesMu.Lock()
				allIssues = append(allIssues, resp.Issues...)
				allIssuesMu.Unlock()

				return nil
			})
		}
	} else {
		g.Go(func() error {
			resp, err := js.jira.SearchIssue(gctx, model.SearchIssueRequest{
				JQL:    baseJQL,
				Expand: jqlConfig.BuildExpand(),
				Fields: jqlConfig.BuildField(),
			})
			if err != nil {
				return fmt.Errorf("search tasks failed: %w", err)
			}

			allIssuesMu.Lock()
			allIssues = append(allIssues, resp.Issues...)
			allIssuesMu.Unlock()

			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}

	return &allIssues, nil
}

// GetRelatedBugsByTasks finds all related Bug issues linked to the given task issues.
// It scans each task's issue links and extracts those where the linked issue is of type "Bug".
// The resulting keys are fetched from JIRA using chunked JQL queries.
//
// Returns all related Bug issues, or an empty list if none are found.
func (js *engJiraService) GetRelatedBugsByTasks(
	ctx context.Context,
	tasks []model.Issue,
	jqlConfig JqlConfig,
) (*[]model.Issue, error) {
	keySet := make(map[string]struct{})
	for _, task := range tasks {
		for _, link := range task.Fields.IssueLinks {
			if link.Inward != nil && link.Inward.Fields.Issuetype.Name == "Bug" {
				keySet[link.Inward.Key] = struct{}{}
			}
			if link.Outward != nil && link.Outward.Fields.Issuetype.Name == "Bug" {
				keySet[link.Outward.Key] = struct{}{}
			}
		}
	}

	var taskKeys []string
	for k := range keySet {
		taskKeys = append(taskKeys, k)
	}

	if len(taskKeys) == 0 {
		return &[]model.Issue{}, nil
	}

	var (
		mu        sync.Mutex
		allIssues []model.Issue
	)
	g, gCtx := errgroup.WithContext(ctx)

	for i := 0; i < len(taskKeys); i += chunkSize {
		start := i
		end := min(i+chunkSize, len(taskKeys))
		chunk := taskKeys[start:end]

		chunkCopy := chunk
		startCopy, endCopy := start, end

		g.Go(func() error {
			keys := make([]string, 0, len(chunkCopy))
			for _, key := range chunkCopy {
				keys = append(keys, fmt.Sprintf(`"%s"`, key))
			}
			jql := fmt.Sprintf("key IN (%s)", strings.Join(keys, ", "))

			result, err := js.jira.SearchIssue(gCtx, model.SearchIssueRequest{
				JQL:    jql,
				Fields: jqlConfig.BuildField(),
				Expand: jqlConfig.BuildExpand(),
			})
			if err != nil {
				log.Println("GetRelatedBugsByTasks error")
				return fmt.Errorf("GetRelatedBugsByTasks failed for chunk [%d:%d]: %w", startCopy, endCopy, err)
			}

			mu.Lock()
			allIssues = append(allIssues, result.Issues...)
			mu.Unlock()

			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}

	return &allIssues, nil
}

// GetChildTaskIssues retrieves child issues for the specified parent keys, chunked to handle large requests.
func (js *engJiraService) GetChildTaskIssues(ctx context.Context, parents []string, jqlConfig JqlConfig) (*[]model.Issue, error) {
	baseJQL := `"Main Project[Checkboxes]" = No`

	if len(parents) == 0 {
		return &[]model.Issue{}, nil
	}

	var (
		mu        sync.Mutex
		allIssues []model.Issue
	)
	g, gCtx := errgroup.WithContext(ctx)

	for i := 0; i < len(parents); i += chunkSize {
		start := i
		end := min(i+chunkSize, len(parents))
		chunk := parents[start:end]

		// safely copy variables into scope
		chunkCopy := append([]string(nil), chunk...)
		startCopy, endCopy := start, end

		g.Go(func() error {
			jql := fmt.Sprintf(`parent IN (%s) AND %s`, strings.Join(chunkCopy, ", "), baseJQL)

			resp, err := js.jira.SearchIssue(gCtx, model.SearchIssueRequest{
				JQL:    jql,
				Expand: jqlConfig.BuildExpand(),
				Fields: jqlConfig.BuildField(),
			})
			if err != nil {
				log.Println("getChildTaskIssues error")
				return fmt.Errorf("getChildTaskIssues failed (chunk %d-%d): %w", startCopy, endCopy, err)
			}

			mu.Lock()
			allIssues = append(allIssues, resp.Issues...)
			mu.Unlock()

			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}

	return &allIssues, nil
}

// GetProductionBug retrieves bugs reported in production within the date range.
func (js *engJiraService) GetProductionBug(ctx context.Context, startDate, endDate string, jqlConfig JqlConfig) (*[]model.Issue, error) {
	jql := fmt.Sprintf(`
		issuetype = Bug AND 
		"Environment Bug[Dropdown]" = Production AND 
		created >= %s AND created < %s`,
		startDate, endDate,
	)
	issues, err := js.jira.SearchIssue(ctx, model.SearchIssueRequest{
		JQL:    jql,
		Fields: jqlConfig.BuildField(),
		Expand: jqlConfig.BuildExpand(),
	})
	if err != nil {
		log.Println("GetProductionBug error")
		return nil, fmt.Errorf("jira.SearchIssue (production bug): %w", err)
	}
	return &issues.Issues, err
}

// GroupConfig holds metadata for group lookup.
type GroupConfig struct {
	GroupName            string
	IncludeInactiveUsers bool
}

// GetEngineer retrieves all users in the "dashboard-engineer" group.
func (js *engJiraService) GetEngineer(ctx context.Context, includeInactiveUsers bool) (*[]model.Author, error) {
	cacheKey := fmt.Sprintf("group:dashboard-engineer:inactive=%v", includeInactiveUsers)

	if v, ok := js.cache.Get(cacheKey); ok {
		if members, ok := v.(*[]model.Author); ok {
			return members, nil
		}
	}

	members, err := js.jira.GroupMember(ctx, model.GroupMemberRequest{
		Name:                 "dashboard-engineer",
		IncludeInactiveUsers: includeInactiveUsers,
	})
	if err != nil {
		return nil, fmt.Errorf("getEngineer failed: %w", err)
	}

	js.cache.Set(cacheKey, members, cache.DefaultExpiration)

	return members, nil
}

// GetQa retrieves all users in the "dashboard-qa" group.
func (js *engJiraService) GetQa(ctx context.Context, includeInactiveUsers bool) (*[]model.Author, error) {
	cacheKey := fmt.Sprintf("group:dashboard-qa:inactive=%v", includeInactiveUsers)

	if v, ok := js.cache.Get(cacheKey); ok {
		if members, ok := v.(*[]model.Author); ok {
			return members, nil
		}
	}

	members, err := js.jira.GroupMember(ctx, model.GroupMemberRequest{
		Name:                 "dashboard-qa",
		IncludeInactiveUsers: includeInactiveUsers,
	})
	if err != nil {
		return nil, fmt.Errorf("getEngineer failed: %w", err)
	}

	js.cache.Set(cacheKey, members, cache.DefaultExpiration)

	return members, nil
}

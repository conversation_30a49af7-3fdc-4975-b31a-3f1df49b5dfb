package eng_test

import (
	"runtime"
	"testing"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/internal/usecase/eng"
	"github.com/Lionparcel/pentools/pkg/timex"
	"github.com/stretchr/testify/suite"
)

type SuiteUsecaseSummary struct {
	suite.Suite
	usecase eng.Usecase
	jira    *MockJiraRepo
	req     dto.GetEngRequest
}

func (s *SuiteUsecaseSummary) SetupTest() {
	s.req = dto.GetEngRequest{}
	s.jira = new(MockJiraRepo)
	js := eng.NewJiraService(s.jira)
	s.usecase = *eng.New(js)
}

func (s *SuiteUsecaseSummary) MockGetEpicIssue(err ...error) {
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	startDate, endDate := timex.GetStartDateEndDateOrDefault(s.req.StartDate, s.req.EndDate)
	s.jira.MockGetEpicIssue(startDate, endDate, true,
		eng.NewJqlConfig([]eng.JiraFieldOption{eng.Status}, nil),
		MockSearchIssueResponse{
			Issues: []model.Issue{
				{
					Fields: model.Fields{
						Status: model.Status{
							Name: "In Progress",
						},
					},
				},
				{
					Fields: model.Fields{
						Status: model.Status{
							Name: "In QA",
						},
					},
				},
				{
					Fields: model.Fields{
						Status: model.Status{
							Name: "Done",
						},
					},
				},
			},
			Err: errResp,
		})
}

func (s *SuiteUsecaseSummary) MockGetQASuccess() {
	s.jira.MockGetQA(false, MockGroupMemberResponse{Member: []model.Author{
		{
			AccountID: "accountQA-01",
		},
	}, Err: nil})
}

func (s *SuiteUsecaseSummary) MockGetEngineer(err ...error) {
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	s.jira.MockGetEngineer(false, MockGroupMemberResponse{Member: []model.Author{
		{
			AccountID: "accountENG-01",
		},
	}, Err: errResp})
}

func (s *SuiteUsecaseSummary) getMockTaskIssuesEngineer() []model.Issue {
	statusInprogress := string(model.IssueStatusInProgress)
	sp := 1.0

	return []model.Issue{
		{
			Fields: model.Fields{
				Assignee: model.Author{AccountID: "accountENG-01"},
				Status:   model.Status{Name: string(model.IssueStatusDone)},
				SP1:      &sp,
				IssueLinks: []model.IssueLink{{
					Inward: &model.LinkedIssueRef{
						Key:    "BUG-01",
						Fields: model.Fields{Issuetype: model.Issuetype{Name: "Bug"}},
					},
				}},
			},
			Changelog: &model.Changelog{Histories: []model.History{
				{Created: "2006-01-02T15:04:05.000+0700", Items: []model.Item{{Field: "status", ToString: &statusInprogress}}},
				{Created: "2006-01-02T16:04:05.000+0700", Items: []model.Item{{Field: "status", FromString: &statusInprogress}}},
			}},
		},
	}
}
func (s *SuiteUsecaseSummary) getMockTaskIssuesQA() []model.Issue {
	statusInQA := string(model.IssueStatusInQA)
	sp := 1.0

	return []model.Issue{
		{
			Fields: model.Fields{
				Assignee: model.Author{AccountID: "accountQA-01"},
				Status:   model.Status{Name: string(model.IssueStatusDone)},
				SP1:      &sp,
				IssueLinks: []model.IssueLink{{
					Outward: &model.LinkedIssueRef{
						Key:    "BUG-01",
						Fields: model.Fields{Issuetype: model.Issuetype{Name: "Bug"}},
					},
				}},
			},
			Changelog: &model.Changelog{Histories: []model.History{
				{Created: "2006-01-03T15:04:05.000+0700", Items: []model.Item{{Field: "status", ToString: &statusInQA}}},
				{Created: "2006-01-03T16:04:05.000+0700", Items: []model.Item{{Field: "status", FromString: &statusInQA}}},
			}},
		},
	}
}

func (s *SuiteUsecaseSummary) MockGetTaskIssuesByAssigneeEnginer(err ...error) {
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	startDate, endDate := timex.GetStartDateEndDateOrDefault(s.req.StartDate, s.req.EndDate)
	s.jira.MockGetTaskIssuesByAssignee(startDate, endDate, []string{
		"accountENG-01"},
		eng.NewJqlConfig([]eng.JiraFieldOption{
			eng.SP,
			eng.Assignee,
			eng.Status,
			eng.IssueLinks,
		}, []eng.JiraExpandOption{eng.Changelog}), MockSearchIssueResponse{
			Issues: s.getMockTaskIssuesEngineer(),
			Err:    errResp})
}

func (s *SuiteUsecaseSummary) MockGetTaskIssuesByAssigneeQA(err ...error) {
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	startDate, endDate := timex.GetStartDateEndDateOrDefault(s.req.StartDate, s.req.EndDate)
	s.jira.MockGetTaskIssuesByAssignee(startDate, endDate, []string{
		"accountQA-01"},
		eng.NewJqlConfig(
			[]eng.JiraFieldOption{
				eng.SP,
				eng.Assignee,
				eng.Status, eng.IssueLinks,
			},
			[]eng.JiraExpandOption{eng.Changelog},
		),
		MockSearchIssueResponse{
			Issues: s.getMockTaskIssuesQA(),
			Err:    errResp})
}

func (s *SuiteUsecaseSummary) MockGetRelatedBugsByTasks(err ...error) {
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	statusRetesting := string(model.IssueStatusRetesting)
	s.jira.MockGetRelatedBugsByTasks([]string{"BUG-01"},
		eng.NewJqlConfig(
			nil,
			[]eng.JiraExpandOption{eng.Changelog},
		),
		MockSearchIssueResponse{
			Issues: []model.Issue{
				{
					Changelog: &model.Changelog{
						Histories: []model.History{
							{
								Created: "2006-01-03T15:04:05.000+0700",
								Items: []model.Item{
									{
										Field:    "status",
										ToString: &statusRetesting,
									},
								},
							},
							{
								Created: "2006-01-03T16:04:05.000+0700",
								Items: []model.Item{
									{
										Field:      "status",
										FromString: &statusRetesting,
									},
								},
							},
						},
					},
				},
			},
			Err: errResp,
		})
}

func (s *SuiteUsecaseSummary) MockGetProductionBug(err ...error) {
	startDate, endDatePlusOne := timex.GetStartDateAndPlusOne(s.req.StartDate, s.req.EndDate)
	var errResp error
	if len(err) > 0 {
		errResp = err[0]
	}
	s.jira.MockGetProductionBug(startDate, endDatePlusOne,
		eng.NewJqlConfig(
			[]eng.JiraFieldOption{eng.FunctionBug}, nil,
		),
		MockSearchIssueResponse{Issues: []model.Issue{
			{},
			{
				Fields: model.Fields{
					FunctionBug: model.CustomField{
						Value: "EnQ",
					},
				},
			},
			{
				Fields: model.Fields{
					FunctionBug: model.CustomField{
						Value: "SA",
					},
				},
			},
			{
				Fields: model.Fields{
					FunctionBug: model.CustomField{
						Value: "Product",
					},
				},
			},
			{
				Fields: model.Fields{
					FunctionBug: model.CustomField{
						Value: "others",
					},
				},
			},
		}, Err: errResp})
}

func (s *SuiteUsecaseSummary) TestGetSummary() {
	s.MockGetEpicIssue()
	s.MockGetQASuccess()
	s.MockGetEngineer()
	s.MockGetTaskIssuesByAssigneeEnginer()
	s.MockGetTaskIssuesByAssigneeQA()
	s.MockGetRelatedBugsByTasks()
	s.MockGetProductionBug()

	resp, err := s.usecase.GetSummary(s.T().Context(), s.req)

	expected := &dto.GetEngSummaryResponse{
		ProductionBug: dto.ProductionBug{
			Total:        5,
			NotDefined:   1,
			FromEngineer: 1,
			Others:       1,
			FromProduct:  1,
			FromSA:       1,
		},
		Member: dto.Member{
			Engineer: dto.MemberMetrics{
				Total:     1,
				Idle:      0,
				HourPerSP: 1,
			},
			QA: dto.MemberMetrics{
				Total:     1,
				Idle:      0,
				HourPerSP: 2,
			},
		},
		Epic: dto.EpicSummary{
			Total:          3,
			InDevelopment:  1,
			InTesting:      1,
			ReadyToRelease: 1,
		},
	}
	s.Equal(expected, resp)
	s.NoError(err)
}

func TestSuiteUsecaseSummary(t *testing.T) {
	suite.Run(t, new(SuiteUsecaseSummary))
}

func BenchmarkGetSummary(b *testing.B) {
	limit := GetLimitBenchmark()
	SimulatedJiraLatency = time.Duration(limit.MaxAllowedDuration) * time.Nanosecond
	defer func() { SimulatedJiraLatency = 0 }() // restore

	limit.MaxAllowedDuration *= 4 // 3 logical step + 1

	var totalAllocs, totalBytes uint64
	b.ReportAllocs()

	jira := new(MockJiraRepo)
	js := eng.NewJiraService(jira)
	uc := *eng.New(js)

	s := &SuiteUsecaseSummary{
		jira:    jira,
		req:     dto.GetEngRequest{},
		usecase: uc,
	}

	// Set up mocks for this run
	s.MockGetEpicIssue()
	s.MockGetQASuccess()
	s.MockGetEngineer()
	s.MockGetTaskIssuesByAssigneeEnginer()
	s.MockGetTaskIssuesByAssigneeQA()
	s.MockGetRelatedBugsByTasks()
	s.MockGetProductionBug()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var memStart, memEnd runtime.MemStats
		runtime.ReadMemStats(&memStart)

		start := time.Now()

		_, err := uc.GetSummary(b.Context(), s.req)
		duration := time.Since(start)

		if err != nil {
			b.Fatalf("unexpected error: %v", err)
		}
		if duration.Nanoseconds() > limit.MaxAllowedDuration {
			b.Fatalf("too slow: %d ns/op exceeds limit of %d ns/op", duration.Nanoseconds(), limit.MaxAllowedDuration)
		}

		runtime.ReadMemStats(&memEnd)
		totalBytes += memEnd.TotalAlloc - memStart.TotalAlloc
		totalAllocs += memEnd.Mallocs - memStart.Mallocs
	}

	avgAllocs := int(totalAllocs / uint64(b.N))
	avgBytes := int(totalBytes / uint64(b.N))

	if avgAllocs > limit.MaxAllocsPerOp {
		b.Fatalf("too many allocs: %d allocs/op exceeds limit of %d", avgAllocs, limit.MaxAllocsPerOp)
	}
	if avgBytes > limit.MaxBytesPerOp {
		b.Fatalf("too much memory: %d B/op exceeds limit of %d", avgBytes, limit.MaxBytesPerOp)
	}
}

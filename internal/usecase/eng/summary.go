package eng

import (
	"context"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/pkg/numeric"
	"github.com/Lionparcel/pentools/pkg/timex"
	"golang.org/x/sync/errgroup"
)

type SummaryData struct {
	ProductionBug   *dto.ProductionBug
	Epic            *dto.EpicSummary
	Engineers       *[]model.Author
	QAs             *[]model.Author
	EngineerMetrics *dto.MemberMetrics
	QAMetrics       *dto.MemberMetrics
}

func (u *Usecase) GetSummary(ctx context.Context, req dto.GetEngRequest) (*dto.GetEngSummaryResponse, error) {
	summaryData, err := u.getData(ctx, req)
	if err != nil {
		return nil, err
	}

	if err := u.getSummaryMetrics(ctx, req, &summaryData); err != nil {
		return nil, err
	}

	return &dto.GetEngSummaryResponse{
		ProductionBug: *summaryData.ProductionBug,
		Epic:          *summaryData.Epic,
		Member: dto.Member{
			Engineer: *summaryData.EngineerMetrics,
			QA:       *summaryData.QAMetrics,
		},
	}, nil
}

func (u *Usecase) getSummaryMetrics(ctx context.Context, req dto.GetEngRequest, summaryData *SummaryData) error {
	g, gCtx := errgroup.WithContext(ctx)
	startDate, endDate := timex.GetStartDateEndDateOrDefault(req.StartDate, req.EndDate)

	g.Go(func() error {
		var err error
		summaryData.EngineerMetrics, err = u.getMetrics(gCtx, summaryData.Engineers, startDate, endDate, false)
		return err
	})

	g.Go(func() error {
		var err error
		summaryData.QAMetrics, err = u.getMetrics(gCtx, summaryData.QAs, startDate, endDate, true)
		return err
	})

	if err := g.Wait(); err != nil {
		return err
	}
	return nil
}

func (u *Usecase) getData(ctx context.Context, req dto.GetEngRequest) (SummaryData, error) {
	var (
		data SummaryData
	)

	g, gCtx := errgroup.WithContext(ctx)

	g.Go(func() error {
		var err error
		data.ProductionBug, err = u.getProductionBug(gCtx, req)
		return err
	})

	g.Go(func() error {
		var err error
		data.Epic, err = u.getEpic(gCtx, req)
		return err
	})

	g.Go(func() error {
		var err error
		data.Engineers, err = u.jiraService.GetEngineer(gCtx, false)
		return err
	})

	g.Go(func() error {
		var err error
		data.QAs, err = u.jiraService.GetQa(gCtx, false)
		return err
	})

	if err := g.Wait(); err != nil {
		return data, err
	}
	return data, nil
}

func (u *Usecase) getProductionBug(ctx context.Context, req dto.GetEngRequest) (*dto.ProductionBug, error) {
	startDate, endDate := timex.GetStartDateAndPlusOne(req.StartDate, req.EndDate)
	issues, err := u.jiraService.GetProductionBug(ctx, startDate, endDate,
		NewJqlConfig([]JiraFieldOption{FunctionBug}, nil),
	)
	if err != nil {
		return nil, err
	}

	counts := u.classifyProductionBugs(*issues)

	return &dto.ProductionBug{
		Total:        len(*issues),
		FromEngineer: counts["EnQ"],
		FromSA:       counts["SA"],
		FromProduct:  counts["Product"],
		Others:       counts["others"],
		NotDefined:   counts["notDefined"],
	}, nil
}

func (u *Usecase) classifyProductionBugs(issues []model.Issue) map[string]int {
	result := map[string]int{
		"EnQ":        0,
		"SA":         0,
		"Product":    0,
		"others":     0,
		"notDefined": 0,
	}

	for _, issue := range issues {
		val := issue.Fields.FunctionBug.Value
		if val == "" {
			result["notDefined"]++
			continue
		}
		switch val {
		case "EnQ", "SA", "Product":
			result[val]++
		default:
			result["others"]++
		}
	}
	return result
}

func (u *Usecase) getMetrics(ctx context.Context, members *[]model.Author, startDate string, endDate string, isQa bool) (*dto.MemberMetrics, error) {
	active := make(map[string]bool)
	var hours, sp, hourPerSP float64
	tasks, err := u.getTasks(ctx, members, startDate, endDate, isQa)
	if err != nil {
		return nil, err
	}

	if isQa {
		retestHours, err := u.getRetestHours(ctx, tasks)
		if err != nil {
			return nil, err
		}
		hours += retestHours
	}

	for _, issue := range *tasks {
		id := issue.Fields.Assignee.AccountID
		active[id] = true
		if issue.Fields.Status.Name == string(model.IssueStatusDone) && issue.Fields.SP() != 0 {
			status := model.IssueStatusInProgress
			if isQa {
				status = model.IssueStatusInQA
			}
			hours += issue.StatusDurationHours(status)
			sp += issue.Fields.SP()
		}
	}

	if sp > 0 {
		hourPerSP = numeric.SafeDivideAndRound(hours, sp, 2)
	}

	return &dto.MemberMetrics{
		Total:     len(*members),
		Idle:      len(*members) - len(active),
		HourPerSP: hourPerSP,
	}, nil
}

func (u *Usecase) getTasks(
	ctx context.Context,
	members *[]model.Author,
	startDate, endDate string,
	isQa bool) (*[]model.Issue, error) {
	assignees := make([]string, 0, len(*members))

	for _, eng := range *members {
		assignees = append(assignees, eng.AccountID)
	}

	fields := []JiraFieldOption{
		SP,
		Assignee,
		Status,
		IssueLinks,
	}
	if isQa {
		fields = append(fields, IssueLinks)
	}
	tasks, err := u.jiraService.GetTaskIssuesByAssignee(ctx, startDate, endDate, assignees,
		NewJqlConfig(fields, []JiraExpandOption{Changelog}),
	)
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (u *Usecase) getRetestHours(ctx context.Context, tasks *[]model.Issue) (float64, error) {
	var retestingHours float64
	devBugs, err := u.jiraService.GetRelatedBugsByTasks(ctx, *tasks,
		NewJqlConfig(nil, []JiraExpandOption{Changelog}))
	if err != nil {
		return 0, err
	}

	for _, bug := range *devBugs {
		retestingHours += bug.StatusDurationHours(model.IssueStatusRetesting)
	}
	return retestingHours, nil
}

func (u *Usecase) getEpic(ctx context.Context, req dto.GetEngRequest) (*dto.EpicSummary, error) {
	startDate, endDate := timex.GetStartDateEndDateOrDefault(req.StartDate, req.EndDate)
	issues, err := u.jiraService.GetEpicIssue(ctx, startDate, endDate, true,
		NewJqlConfig([]JiraFieldOption{Status}, nil))
	if err != nil {
		return nil, err
	}

	var (
		inDevelopment  []model.Issue
		inTesting      []model.Issue
		readyToRelease []model.Issue
	)

	for _, issue := range *issues {
		switch val := issue.Fields.Status.Name; val {
		case "In Progress":
			inDevelopment = append(inDevelopment, issue)
		case "In QA":
			inTesting = append(inTesting, issue)
		case "Done":
			readyToRelease = append(readyToRelease, issue)
		}
	}

	return &dto.EpicSummary{
		Total:          len(*issues),
		InDevelopment:  len(inDevelopment),
		InTesting:      len(inTesting),
		ReadyToRelease: len(readyToRelease),
	}, nil
}

package eng_test

import (
	"runtime"
	"testing"
	"time"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/internal/usecase/eng"
	"github.com/Lionparcel/pentools/pkg/timex"
	"github.com/stretchr/testify/suite"
)

type SuiteTestGetBug struct {
	suite.Suite
	usecase eng.Usecase
	jira    *MockJiraRepo
	req     dto.GetEngRequest
}

func (s *SuiteTestGetBug) SetupTest() {
	s.req = dto.GetEngRequest{}
	s.jira = new(MockJiraRepo)
	js := eng.NewJiraService(s.jira)
	s.usecase = *eng.New(js)
}

func (s *SuiteTestGetBug) MockProdBug() model.Issue {
	var fixing = "[fixing]"
	var testingComment = "[testing]"

	return model.Issue{
		Key: "PROD-BUG-01",
		Fields: model.Fields{
			Created: "2006-01-02T16:04:05.000+0700",
			EnvironmentBug: model.CustomField{
				Value: "Production",
			},
			FunctionBug: model.CustomField{
				Value: "Function",
			},
			Summary: "Summary",
			AccidentBug: model.CustomField{
				Value: "Accident Bug",
			},
			SeverityBug: model.CustomField{
				Value: "Severity",
			},
			Squad: model.CustomField{
				Value: "Squad",
			},
			Worklog: model.FieldsWorklog{
				Worklogs: []model.WorklogElement{
					{
						TimeSpentSeconds: 3600,
						Comment: &model.Comment{
							Content: []model.CommentContent{
								{
									Type: "paragraph",
									Content: []model.ContentContent{
										{Text: &fixing, Type: "text"},
									},
								},
							},
						},
					},
					{
						TimeSpentSeconds: 3600,
						Comment: &model.Comment{
							Content: []model.CommentContent{
								{
									Type: "paragraph",
									Content: []model.ContentContent{
										{Text: &testingComment, Type: "text"},
									},
								},
							},
						},
					},
				},
			},
		},
	}
}

func (s *SuiteTestGetBug) MockDevBug() model.Issue {
	return model.Issue{
		Key: "DEV-BUG-01",
		Fields: model.Fields{
			SeverityBug:    model.CustomField{Value: "Dev Severity"},
			Squad:          model.CustomField{Value: "Dev Squad"},
			AccidentBug:    model.CustomField{Value: "Dev Accident"},
			DevAssignee:    model.Author{DisplayName: "Engineer 1"},
			Summary:        "Dev Summary",
			Created:        "2006-01-02T15:04:05.000+0700",
			EnvironmentBug: model.CustomField{Value: "Development"},
		},
		Changelog: &model.Changelog{
			Histories: []model.History{
				{
					Created: "2006-01-02T16:04:05.000+0700",
					Items: []model.Item{
						{
							Field:    "status",
							ToString: &readyToTestStatus,
						},
					},
				},
			},
		},
	}
}

func (s *SuiteTestGetBug) MockGetProdBug(startDate, endDate string, useEmptyIssue bool) {
	bug := s.MockProdBug()
	if useEmptyIssue {
		bug = model.Issue{}
	}
	s.jira.MockGetProductionBug(startDate, endDate,
		eng.NewJqlConfig([]eng.JiraFieldOption{
			eng.AccidentBug,
			eng.SeverityBug,
			eng.SquadBug,
			eng.Summary,
			eng.Worklog,
			eng.FunctionBug,
			eng.EnvironmentBug,
			eng.Created,
		}, nil), MockSearchIssueResponse{
			Issues: []model.Issue{
				bug,
			},
		})
}

func (s *SuiteTestGetBug) MockGetDevBug(startDate, endDate string, useEmptyIssue bool) {
	bug := s.MockDevBug()
	if useEmptyIssue {
		bug = model.Issue{}
	}
	s.jira.MockGetDevBugByDevAssignee(startDate, endDate, []string{}, eng.NewJqlConfig(
		[]eng.JiraFieldOption{
			eng.SeverityBug,
			eng.SquadBug,
			eng.AccidentBug,
			eng.DevAssignee,
			eng.Summary,
			eng.Created,
			eng.EnvironmentBug,
		},
		[]eng.JiraExpandOption{
			eng.Changelog,
		},
	), MockSearchIssueResponse{
		Issues: []model.Issue{
			bug,
		},
	})
}

func (s *SuiteTestGetBug) TestGetBug() {
	startDate, endDate := timex.GetStartDateAndPlusOne(s.req.StartDate, s.req.EndDate)
	s.MockGetProdBug(startDate, endDate, false)
	s.MockGetDevBug(startDate, endDate, false)

	resp, err := s.usecase.GetBug(s.T().Context(), s.req)
	expected := s.getExpected()

	s.Equal(expected, resp)
	s.NoError(err)
}

func (s *SuiteTestGetBug) TestGetBugEmptyBug() {
	startDate, endDate := timex.GetStartDateAndPlusOne(s.req.StartDate, s.req.EndDate)
	s.MockGetProdBug(startDate, endDate, true)
	s.MockGetDevBug(startDate, endDate, true)

	resp, err := s.usecase.GetBug(s.T().Context(), s.req)

	s.NotNil(resp)
	s.NoError(err)
}

func (s *SuiteTestGetBug) getExpected() *dto.GetEngBugResponse {
	prodBugCreated, devBugCreated := s.getExpectedDate()

	expected := &dto.GetEngBugResponse{
		ProdBug: dto.ProdBugResponse{
			BugResponse: dto.BugResponse{
				Total: 1,
				Severity: map[string]int{
					"Severity": 1,
				},
				Squad: map[string]int{
					"Squad": 1,
				},
				AccidentType: map[string]int{
					"Accident Bug": 1,
				},
			},
			Function: map[string]int{
				"Function": 1,
			},
			Bugs: []dto.ProdBugDetail{
				{
					BugDetail: dto.BugDetail{
						Summary:        "Summary",
						Severity:       "Severity",
						AccidentBug:    "Accident Bug",
						ResolutionTime: 120.0,
						Squad:          "Squad",
						CreatedDate:    prodBugCreated,
						Url:            "https://lionparcel.atlassian.net/browse/PROD-BUG-01",
					},
					FixingTime:  60.0,
					TestingTime: 60.0,
					Function:    "Function",
				},
			},
		},
		DevBug: dto.DevBugResponse{
			BugResponse: dto.BugResponse{
				Total: 1,
				Severity: map[string]int{
					"Dev Severity": 1,
				},
				Squad: map[string]int{
					"Dev Squad": 1,
				},
				AccidentType: map[string]int{
					"Dev Accident": 1,
				},
			},
			Engineer: map[string]int{
				"Engineer 1": 1,
			},
			Bugs: []dto.DevBugDetail{
				{
					BugDetail: dto.BugDetail{
						Summary:        "Dev Summary",
						Severity:       "Dev Severity",
						AccidentBug:    "Dev Accident",
						ResolutionTime: 60.0,
						Squad:          "Dev Squad",
						CreatedDate:    devBugCreated,
						Url:            "https://lionparcel.atlassian.net/browse/DEV-BUG-01",
					},
					Assignee: "Engineer 1",
				},
			},
		},
	}
	return expected
}

func (s *SuiteTestGetBug) getExpectedDate() (time.Time, time.Time) {
	loc, _ := time.LoadLocation("Asia/Jakarta")
	prodBugCreated, err := time.ParseInLocation(timex.JiraDateTimeLayout, "2006-01-02T16:04:05.000+0700", loc)
	if err != nil {
		s.T().Error(err)
	}
	devBugCreated, err := time.ParseInLocation(timex.JiraDateTimeLayout, "2006-01-02T15:04:05.000+0700", loc)
	if err != nil {
		s.T().Error(err)
	}
	return prodBugCreated, devBugCreated
}

func TestSuiteTestGetBug(t *testing.T) {
	suite.Run(t, new(SuiteTestGetBug))
}

func BenchmarkGetBug(b *testing.B) {
	limit := GetLimitBenchmark()
	SimulatedJiraLatency = time.Duration(limit.MaxAllowedDuration) * time.Nanosecond
	defer func() { SimulatedJiraLatency = 0 }()
	limit.MaxAllowedDuration *= 2

	var totalAllocs, totalBytes uint64
	b.ReportAllocs()

	jira := new(MockJiraRepo)
	js := eng.NewJiraService(jira)
	uc := *eng.New(js)

	s := &SuiteTestGetBug{
		jira:    jira,
		req:     dto.GetEngRequest{},
		usecase: uc,
	}

	// Setup necessary mocks
	startDate, endDate := timex.GetStartDateAndPlusOne(s.req.StartDate, s.req.EndDate)
	s.MockGetProdBug(startDate, endDate, false)
	s.MockGetDevBug(startDate, endDate, false)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var memStart, memEnd runtime.MemStats
		runtime.ReadMemStats(&memStart)

		start := time.Now()
		_, err := uc.GetBug(b.Context(), s.req)
		duration := time.Since(start)

		if err != nil {
			b.Fatalf("unexpected error: %v", err)
		}
		if duration.Nanoseconds() > limit.MaxAllowedDuration {
			b.Fatalf("too slow: %d ns/op exceeds limit of %d ns/op", duration.Nanoseconds(), limit.MaxAllowedDuration)
		}

		runtime.ReadMemStats(&memEnd)
		totalBytes += memEnd.TotalAlloc - memStart.TotalAlloc
		totalAllocs += memEnd.Mallocs - memStart.Mallocs
	}

	avgAllocs := int(totalAllocs / uint64(b.N))
	avgBytes := int(totalBytes / uint64(b.N))

	if avgAllocs > limit.MaxAllocsPerOp {
		b.Fatalf("too many allocs: %d allocs/op exceeds limit of %d", avgAllocs, limit.MaxAllocsPerOp)
	}
	if avgBytes > limit.MaxBytesPerOp {
		b.Fatalf("too much memory: %d B/op exceeds limit of %d", avgBytes, limit.MaxBytesPerOp)
	}
}

package eng_test

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/pentools/internal/model"
	"github.com/Lionparcel/pentools/internal/usecase/eng"
	"github.com/stretchr/testify/mock"
)

var SimulatedJiraLatency = 0 * time.Millisecond // default = no delay

type MockJiraRepo struct {
	mock.Mock
}

type MockSearchIssueResponse struct {
	Issues []model.Issue
	Err    error
}

type MockGroupMemberResponse struct {
	Member []model.Author
	Err    error
}

func (m *MockJiraRepo) SearchIssue(ctx context.Context, req model.SearchIssueRequest) (*model.SearchIssueResponse, error) {
	time.Sleep(SimulatedJiraLatency) // simulate latency
	args := m.Called(ctx, req)
	if res, ok := args.Get(0).(*model.SearchIssueResponse); ok {
		return res, args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *MockJiraRepo) GroupMember(ctx context.Context, req model.GroupMemberRequest) (*[]model.Author, error) {
	time.Sleep(SimulatedJiraLatency) // simulate latency
	args := m.Called(ctx, req)
	if res, ok := args.Get(0).(*[]model.Author); ok {
		return res, args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *MockJiraRepo) MockGetChildTaskIssues(parents []string, jqlConfig eng.JqlConfig, resp MockSearchIssueResponse) {
	baseJQL := `"Main Project[Checkboxes]" = No`
	jql := fmt.Sprintf(`parent IN (%s) AND %s`, strings.Join(parents, ", "), baseJQL)
	expands := jqlConfig.BuildExpand()
	fields := jqlConfig.BuildField()
	m.On("SearchIssue", mock.Anything, model.SearchIssueRequest{
		JQL:    jql,
		Expand: expands,
		Fields: fields,
	}).Return(&model.SearchIssueResponse{
		Issues: resp.Issues,
	}, nil).Maybe()
}

func (m *MockJiraRepo) MockGetEpicIssue(startDate, endDate string, isActiveSprint bool, jqlConfig eng.JqlConfig, resp MockSearchIssueResponse) {
	jql := fmt.Sprintf(`
		"Main Project[Checkboxes]" = Yes AND 
		"Expected Start Date[Date]" <= %s AND 
		"Expected End Date[Date]" >= %s`,
		endDate, startDate,
	)
	if isActiveSprint {
		jqlConfig.AddFields([]eng.JiraFieldOption{eng.FixVersion})

	}
	expands := jqlConfig.BuildExpand()
	fields := jqlConfig.BuildField()

	req := model.SearchIssueRequest{
		JQL:    jql,
		Fields: fields,
		Expand: expands,
	}
	m.On("SearchIssue", mock.Anything, req).Return(&model.SearchIssueResponse{
		Issues: resp.Issues,
	}, resp.Err).Maybe()
}

func (m *MockJiraRepo) MockGetQA(includeInactiveUsers bool, resp MockGroupMemberResponse) {
	m.On("GroupMember", mock.Anything, model.GroupMemberRequest{
		Name:                 "dashboard-qa",
		IncludeInactiveUsers: includeInactiveUsers,
	}).Return(&resp.Member, resp.Err).Maybe()
}

func (m *MockJiraRepo) MockGetEngineer(includeInactiveUsers bool, resp MockGroupMemberResponse) {
	m.On("GroupMember", mock.Anything, model.GroupMemberRequest{
		Name:                 "dashboard-engineer",
		IncludeInactiveUsers: includeInactiveUsers,
	}).Return(&resp.Member, resp.Err).Maybe()
}

func (m *MockJiraRepo) MockGetProductionBug(startDate, endDate string, jqlConfig eng.JqlConfig, resp MockSearchIssueResponse) {
	jql := fmt.Sprintf(`
		issuetype = Bug AND 
		"Environment Bug[Dropdown]" = Production AND 
		created >= %s AND created < %s`,
		startDate, endDate,
	)
	expands := jqlConfig.BuildExpand()
	fields := jqlConfig.BuildField()

	m.On("SearchIssue", mock.Anything, model.SearchIssueRequest{
		JQL:    jql,
		Fields: fields,
		Expand: expands,
	}).Return(&model.SearchIssueResponse{
		Issues: resp.Issues,
	}, resp.Err).Maybe()
}

func (m *MockJiraRepo) MockGetTaskIssuesByAssignee(startDate, endDate string, assignees []string, jqlConfig eng.JqlConfig, resp MockSearchIssueResponse) {
	baseJQL := fmt.Sprintf(`
		"Main Project[Checkboxes]" = No AND
		"Expected Start Date[Date]" <= %s AND 
		"Expected End Date[Date]" >= %s`, endDate, startDate)

	jql := fmt.Sprintf(`assignee IN (%s) AND %s`, strings.Join(assignees, ", "), baseJQL)

	expands := jqlConfig.BuildExpand()
	fields := jqlConfig.BuildField()

	m.On("SearchIssue", mock.Anything, model.SearchIssueRequest{
		JQL:    jql,
		Expand: expands,
		Fields: fields,
	}).Return(&model.SearchIssueResponse{
		Issues: resp.Issues,
	}, resp.Err).Maybe()
}

func (m *MockJiraRepo) MockGetDevBugByDevAssignee(startDate, endDate string, assignees []string, jqlConfig eng.JqlConfig, resp MockSearchIssueResponse) {
	baseJQL := fmt.Sprintf(`
		issuetype = Bug and 
		status != Backlog and
		"Environment Bug[Dropdown]" IN (Development, Staging) and
		"Accident Bug[Dropdown]" = "Bug in Test Case" and
		created < %s and created >= %s`, endDate, startDate)

	jql := baseJQL
	if len(assignees) > 0 {
		jql = fmt.Sprintf(`"Dev Assignee[User Picker (single user)]" IN (%s) AND %s`, strings.Join(assignees, ", "), baseJQL)

	}

	expands := jqlConfig.BuildExpand()
	fields := jqlConfig.BuildField()

	m.On("SearchIssue", mock.Anything, model.SearchIssueRequest{
		JQL:    jql,
		Expand: expands,
		Fields: fields,
	}).Return(&model.SearchIssueResponse{
		Issues: resp.Issues,
	}, resp.Err).Maybe()
}

func (m *MockJiraRepo) MockGetRelatedBugsByTasks(keys []string, jqlConfig eng.JqlConfig, resp MockSearchIssueResponse) {
	quoted := make([]string, 0, len(keys))
	for _, a := range keys {
		quoted = append(quoted, fmt.Sprintf(`"%s"`, a))
	}
	jql := fmt.Sprintf("key IN (%s)", strings.Join(quoted, ", "))
	expands := jqlConfig.BuildExpand()
	fields := jqlConfig.BuildField()

	m.On("SearchIssue", mock.Anything, model.SearchIssueRequest{
		JQL:    jql,
		Fields: fields,
		Expand: expands,
	}).Return(&model.SearchIssueResponse{
		Issues: resp.Issues,
	}, resp.Err).Maybe()
}

var mockEpic = model.Issue{
	Key: "EPIC-01",
	Fields: model.Fields{
		Summary: "Epic Name 01",
		Reporter: model.Author{
			DisplayName: "Reporter 01",
		},
		EngExpectedStartDate: "2025-07-03",
		EngExpectedEndDate:   "2025-07-05",
		Status: model.Status{
			Name: "In Progress",
		},
		FixVersion: []model.FixVersion{
			{
				Name: "Fix Version",
			},
		},
	},
}
var mockQA = model.Author{
	AccountID:   "accountQA-01",
	DisplayName: "QA 2",
}

var mockEngineer = model.Author{
	AccountID:   "accountENG-01",
	DisplayName: "Engineer 1",
}

var mockStory = model.Issue{
	Key: "STORY-01",
	Fields: model.Fields{
		Parent: &mockEpic,
	},
}

var mockTaskQA = model.Issue{
	Key: "TASK-QA-01",
	Fields: model.Fields{
		Status: model.Status{
			Name: string(model.IssueStatusDone),
		},
		Summary:              "Task QA 1",
		Parent:               &mockEpic,
		EngExpectedStartDate: "2025-07-03",
		EngExpectedEndDate:   "2025-07-03",
		SP1:                  &sp,
		Assignee:             mockQA,
		IssueLinks: []model.IssueLink{
			{
				Outward: &model.LinkedIssueRef{
					Key: mockBug.Key,
					Fields: model.Fields{
						Issuetype: model.Issuetype{
							Name: "Bug",
						},
					},
				},
			},
		},
	},
	Changelog: &model.Changelog{
		Histories: []model.History{
			{
				Created: "2025-07-03T15:00:00.000+0700",
				Items: []model.Item{
					{
						Field:      "status",
						ToString:   &doneStatus,
						FromString: &inQaStatus,
					},
				},
			},
			{
				Created: "2025-07-03T14:00:00.000+0700",
				Items: []model.Item{
					{
						Field:    "status",
						ToString: &inQaStatus,
					},
				},
			},
		},
	},
}

var mockSubTask = model.Issue{
	Key: "SUBTASK-01",
	Fields: model.Fields{
		Status: model.Status{
			Name: string(model.IssueStatusDone),
		},
		Summary:              "Task Engineer 1",
		Parent:               &mockStory,
		EngExpectedStartDate: "2025-07-02",
		EngExpectedEndDate:   "2025-07-02",
		SP1:                  &sp,
		Assignee:             mockEngineer,
		IssueLinks: []model.IssueLink{
			{
				Inward: &model.LinkedIssueRef{
					Key: mockBug.Key,
					Fields: model.Fields{
						Issuetype: model.Issuetype{
							Name: "Bug",
						},
					},
				},
			},
		},
	},
	Changelog: &model.Changelog{
		Histories: []model.History{
			{
				Created: "2025-07-02T15:00:00.000+0700",
				Items: []model.Item{
					{
						Field:      "status",
						ToString:   &readyToTestStatus,
						FromString: &inProgressStatus,
					},
				},
			},
			{
				Created: "2025-07-02T14:00:00.000+0700",
				Items: []model.Item{
					{
						Field:    "status",
						ToString: &inProgressStatus,
					},
				},
			},
		},
	},
}

var mockSubTaskFromProduct = model.Issue{
	Key: "SUBTASK-02",
	Fields: model.Fields{
		Parent:               &mockStory,
		EngExpectedStartDate: "2025-07-02",
		EngExpectedEndDate:   "2025-07-02",
		SP1:                  &sp,
		Assignee:             mockEngineer,
		AdditionalTask:       model.CustomField{Value: "From Product"},
		IssueLinks:           []model.IssueLink{},
	},
	Changelog: &model.Changelog{
		Histories: []model.History{
			{
				Created: "2025-07-02T15:00:00.000+0700",
				Items: []model.Item{
					{
						Field:      "status",
						ToString:   &readyToTestStatus,
						FromString: &inProgressStatus,
					},
				},
			},
			{
				Created: "2025-07-02T14:00:00.000+0700",
				Items: []model.Item{
					{
						Field:    "status",
						ToString: &inProgressStatus,
					},
				},
			},
		},
	},
}

var mockSubTaskFromSA = model.Issue{
	Key: "SUBTASK-02",
	Fields: model.Fields{
		Parent:               &mockStory,
		EngExpectedStartDate: "2025-07-02",
		EngExpectedEndDate:   "2025-07-02",
		SP1:                  &sp,
		Assignee:             mockEngineer,
		AdditionalTask:       model.CustomField{Value: "From SA"},
		IssueLinks:           []model.IssueLink{},
	},
	Changelog: &model.Changelog{
		Histories: []model.History{
			{
				Created: "2025-07-03T15:00:00.000+0700",
				Items: []model.Item{
					{
						Field:      "status",
						ToString:   &readyToTestStatus,
						FromString: &inProgressStatus,
					},
				},
			},
			{
				Created: "2025-07-03T14:00:00.000+0700",
				Items: []model.Item{
					{
						Field:    "status",
						ToString: &inProgressStatus,
					},
				},
			},
		},
	},
}

var mockBug = model.Issue{
	Key: "BUG-01",
	Fields: model.Fields{
		AccidentBug: model.CustomField{
			Value: "Bug in Test Case",
		},
		DevAssignee: mockEngineer,
	},
	Changelog: &model.Changelog{
		Histories: []model.History{
			{
				Created: "2025-07-04T15:00:00.000+0700",
				Items: []model.Item{
					{
						Field:    "status",
						ToString: &retestingStatus,
					},
				},
			},
			{
				Created: "2025-07-04T16:00:00.000+0700",
				Items: []model.Item{
					{
						Field:      "status",
						FromString: &retestingStatus,
					},
				},
			},
		},
	},
}

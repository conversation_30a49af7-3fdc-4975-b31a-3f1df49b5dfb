package http

import (
	"context"

	"github.com/Lionparcel/pentools/internal/dto"
	"github.com/Lionparcel/pentools/pkg/config"
	"github.com/labstack/echo/v4"
)

type Usecase interface {
	GetSummary(ctx context.Context, req dto.GetEngRequest) (*dto.GetEngSummaryResponse, error)
	GetEpic(ctx context.Context, req dto.GetEngEpicRequest) (*dto.GetEngEpicResponse, error)
	GetLeaderboard(ctx context.Context, req dto.GetEngRequest) (*dto.GetEngLeaderboardResponse, error)
	GetBug(ctx context.Context, req dto.GetEngRequest) (*dto.GetEngBugResponse, error)
}
type SEHTTPHandler struct {
	usecase Usecase
}

func New(_ *config.Config, e *echo.Echo, usecase Usecase) {
	h := &SEHTTPHandler{
		usecase: usecase,
	}
	e.GET("/api/eng/summary", h.GetSummary)
	e.GET("/api/eng/epic", h.GetEpic)
	e.GET("/api/eng/leaderboard", h.GetLeaderboard)
	e.GET("/api/eng/bug", h.GetBug)

}

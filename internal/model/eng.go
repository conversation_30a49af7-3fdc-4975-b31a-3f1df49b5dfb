package model

import (
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/Lionparcel/pentools/pkg/timex"
)

var loc = func() *time.Location {
	loc, _ := time.LoadLocation("Asia/Jakarta")

	return loc
}

// StatusDurationHours calculates the total duration (in hours) that an issue spent in the given status.
// It analyzes the changelog to detect when the issue entered and exited the specified status.
//
// For each matching status transition:
// - When `ToString` matches the target status, it marks an entry timestamp.
// - When `FromString` matches the target status, it marks an exit timestamp.
//
// If the issue is still currently in the specified status (i.e., more entries than exits),
// the current time is assumed as the exit timestamp.
//
// If the issue never entered the specified status (i.e., no matching `ToString`), it returns 0.
//
// Returns the total time spent in the status, in hours.
func (i Issue) StatusDurationHours(status IssueStatus) float64 {
	if i.Changelog == nil {
		return float64(0)
	}

	var startTimes, endTimes []time.Time

	for _, history := range i.Changelog.Histories {
		createdTime, err := time.ParseInLocation(timex.JiraDateTimeLayout, history.Created, loc())
		if err != nil {
			continue
		}

		for _, item := range history.Items {
			if item.Field == "status" {
				if item.ToString != nil && *item.ToString == string(status) {
					startTimes = append(startTimes, createdTime)
				}
				if item.FromString != nil && *item.FromString == string(status) {
					endTimes = append(endTimes, createdTime)
				}
			}
		}
	}

	// still in status
	if len(startTimes) > len(endTimes) {
		endTimes = append(endTimes, time.Now())

	}

	// never in status
	if len(startTimes) == 0 {
		return float64(0)
	}

	// Sort to ensure matching pairs
	slices.SortFunc(startTimes, compareTime)
	slices.SortFunc(endTimes, compareTime)

	// sum durations
	total := time.Duration(0)
	for idx := 0; idx < len(startTimes) && idx < len(endTimes); idx++ {
		if endTimes[idx].After(startTimes[idx]) {
			total += endTimes[idx].Sub(startTimes[idx])
		}
	}

	durationsHours := total.Hours()

	return durationsHours
}

// StatusTime returns the timestamp when the issue entered the specified status.
// If latestFirst is true, it returns the most recent entry time.
// If latestFirst is false, it returns the earliest entry time.
//
// If changelog is nil or no matching status transition is found, it returns nil.
func (i Issue) StatusTime(status IssueStatus, latestFirst bool) *time.Time {
	if i.Changelog == nil {
		return nil
	}

	var statusTimes []time.Time

	for _, history := range i.Changelog.Histories {
		createdTime, err := time.ParseInLocation(timex.JiraDateTimeLayout, history.Created, loc())
		if err != nil {
			continue
		}
		for _, item := range history.Items {
			if item.Field == "status" && item.ToString != nil && *item.ToString == string(status) {
				statusTimes = append(statusTimes, createdTime)
			}
		}
	}

	if len(statusTimes) == 0 {
		return nil
	}

	if latestFirst {
		slices.SortFunc(statusTimes, func(a, b time.Time) int { return compareTime(b, a) })
	} else {
		slices.SortFunc(statusTimes, compareTime)
	}

	return &statusTimes[0]
}

func compareTime(a, b time.Time) int {
	if a.Before(b) {
		return -1
	}
	if a.After(b) {
		return 1
	}
	return 0
}

// EngOntimeStatus determines whether the issue was completed on or before its expected end date.
//
// It parses the `EngExpectedEndDate` field and compares it against the timestamp when the issue entered
// the "Done" status. If the issue is not yet completed (i.e., never reached "Done"), it uses the current time
// for comparison.
//
// Returns a pointer to a boolean:
// - `true` if the issue was completed on time or early.
// - `false` if it was completed late.
// - Returns an error if the expected date is missing or invalid.
func (i Issue) EngOntimeStatus(isQa bool) (*bool, error) {
	expected := i.Fields.EngExpectedEndDate
	if expected == "" {
		return nil, fmt.Errorf("missing expected end date")
	}

	expectedTime, err := time.ParseInLocation(time.DateOnly, expected, loc())
	if err != nil {
		return nil, fmt.Errorf("invalid expected end date: %w", err)
	}

	checkTime := i.ActualEndDate(isQa)

	// still in progress
	if checkTime == nil {
		now := time.Now()
		checkTime = &now
	}

	isOntime := checkTime.Before(expectedTime.AddDate(0, 0, 1).Add(9 * time.Hour))
	return &isOntime, nil
}

func (f Fields) EngExpectedStartDateAsDate() time.Time {
	return parseDateInJakarta(f.EngExpectedStartDate, false)
}

func (f Fields) EngExpectedEndDateAsDate() time.Time {
	return parseDateInJakarta(f.EngExpectedEndDate, false)
}

func (f Fields) CreatedAsDate() time.Time {
	return parseDateInJakarta(f.Created, true)
}

func parseDateInJakarta(dateStr string, includeHours bool) time.Time {
	format := time.DateOnly
	if includeHours {
		format = timex.JiraDateTimeLayout
	}
	t, err := time.ParseInLocation(format, dateStr, loc())
	if err != nil {
		return time.Time{}
	}
	return t
}

func (i *Issue) IsValidTask() bool {
	return i.Fields.EngExpectedStartDate != "" && i.Fields.EngExpectedEndDate != ""
}

func (i *Issue) ActualStartDate(isQa bool) *time.Time {
	if isQa {
		return i.StatusTime(IssueStatusInQA, false)
	}
	return i.StatusTime(IssueStatusInProgress, false)
}

func (i *Issue) ActualEndDate(isQa bool) *time.Time {
	if isQa {
		if i.StatusTime(IssueStatusStgReadyToDeploy, true) != nil {
			return i.StatusTime(IssueStatusStgReadyToDeploy, true)
		}
		return i.StatusTime(IssueStatusDone, true)
	}
	if i.StatusTime(IssueStatusReadyToTest, true) != nil {
		return i.StatusTime(IssueStatusReadyToTest, true)
	}
	return i.StatusTime(IssueStatusDone, true)
}

func (i *Issue) BugList() map[string]bool {
	keySet := make(map[string]bool)
	for _, link := range i.Fields.IssueLinks {
		if link.Inward != nil && link.Inward.Fields.Issuetype.Name == "Bug" {
			keySet[link.Inward.Key] = true
		}
		if link.Outward != nil && link.Outward.Fields.Issuetype.Name == "Bug" {
			keySet[link.Outward.Key] = true
		}
	}

	return keySet
}

func (i *Issue) IsEngineeringBug() bool {
	switch i.Fields.AccidentBug.Value {
	case "Bug in Test Case":
		return true
	default:
		return false
	}
}

func (i *Issue) ResolutionFixingTimeInSeconds() int64 {
	envBug := i.Fields.EnvironmentBug.Value

	var fixingSeconds int64
	switch envBug {
	case "Production":
		for _, wg := range i.Fields.Worklog.Worklogs {
			comment := wg.FormattedComment()
			if strings.Contains(strings.ToLower(comment), "[fixing]") {
				fixingSeconds += wg.TimeSpentSeconds
			}
		}
	case "Staging", "Development":
		created := parseDateInJakarta(i.Fields.Created, true)
		done := i.ActualEndDate(false)
		if done == nil {
			return 0
		}
		duration := done.Sub(created)
		fixingSeconds = int64(duration.Seconds())
	}

	return fixingSeconds
}

func (i *Issue) ResolutionTestingTimeInSeconds() int64 {
	envBug := i.Fields.EnvironmentBug.Value

	var testing int64
	switch envBug {
	case "Production":
		for _, wg := range i.Fields.Worklog.Worklogs {
			comment := wg.FormattedComment()
			if strings.Contains(strings.ToLower(comment), "[testing]") {
				testing += wg.TimeSpentSeconds
			}
		}
	}
	return testing
}

func (i *Issue) ResolutionTimeInSeconds() int64 {
	return i.ResolutionFixingTimeInSeconds() + i.ResolutionTestingTimeInSeconds()
}

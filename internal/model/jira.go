package model

import (
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/Lionparcel/pentools/pkg/timex"
)

type SearchIssueColumnName int

const (
	SearchIssueColumnNamePEName SearchIssueColumnName = iota
	SearchIssueColumnNameType
	SearchIssueColumnNameIssueName
	SearchIssueColumnNameLinkTicket
	SearchIssueColumnNameSP
	SearchIssueColumnNameTicketCreatedAt
	SearchIssueColumnNameWorkLogDate
	SearchIssueColumnNameWorkLogTimeSpentHour
	SearchIssueColumnNameWorkLogTimeSpentHuman
	SearchIssueColumnNameWorkLogComment
)

type BreakDownStatus string

const (
	BreakDownStatusDone       BreakDownStatus = "Done"
	BreakDownStatusInProgress BreakDownStatus = "In Progress"
	BreakDownStatusBacklog    BreakDownStatus = "Backlog"
)

type IssueStatus string

const (
	IssueStatusDone             IssueStatus = "Done"
	IssueStatusReadyToTest      IssueStatus = "Ready to Test"
	IssueStatusInProgress       IssueStatus = "In Progress"
	IssueStatusInQA             IssueStatus = "In QA"
	IssueStatusBacklog          IssueStatus = "Backlog"
	IssueStatusRetesting        IssueStatus = "Retesting"
	IssueStatusStgReadyToDeploy IssueStatus = "STG - Ready to Deploy"
)

func (c SearchIssueColumnName) String() string {
	switch c {
	case SearchIssueColumnNamePEName:
		return "PE Name"
	case SearchIssueColumnNameType:
		return "Type"
	case SearchIssueColumnNameIssueName:
		return "Issue Name"
	case SearchIssueColumnNameLinkTicket:
		return "Link Ticket"
	case SearchIssueColumnNameSP:
		return "SP"
	case SearchIssueColumnNameTicketCreatedAt:
		return "Ticket Created At"
	case SearchIssueColumnNameWorkLogDate:
		return "Work Log Date"
	case SearchIssueColumnNameWorkLogTimeSpentHour:
		return "Work Log Time Spent (hour)"
	case SearchIssueColumnNameWorkLogTimeSpentHuman:
		return "Work Log Time Spent"
	case SearchIssueColumnNameWorkLogComment:
		return "Work Log Comment"
	default:
		return ""
	}
}

type GroupMemberRequest struct {
	Name                 string `json:"name"`
	StartAt              int    `json:"startAt"`
	IncludeInactiveUsers bool   `json:"includeInactiveUsers"`
}

type SearchIssueRequest struct {
	JQL           string `json:"jql"`
	Fields        string `json:"fields"`
	StartDate     string `json:"start_date"`
	EndDate       string `json:"end_date"`
	NextPageToken string
	Expand        string
}

type CountIssueRequest struct {
	JQL string `json:"jql"`
}

type CountIssueResponse struct {
	Count int `json:"count"`
}

type GroupMemberResponse struct {
	IsLast bool     `json:"isLast"`
	Values []Author `json:"values"`
}

type SearchIssueResponse struct {
	Issues        []Issue            `json:"issues"`
	Params        SearchIssueRequest `json:"params"`
	NextPageToken *string            `json:"nextPageToken"`
}

type Issue struct {
	Expand     string     `json:"expand"`
	ID         string     `json:"id"`
	Self       string     `json:"self"`
	Key        string     `json:"key"`
	Fields     Fields     `json:"fields"`
	Changelog  *Changelog `json:"changelog,omitempty"`
	ChildIssue *[]Issue   `json:"child_issue,omitempty"`
}
type Fields struct {
	Issuetype            Issuetype     `json:"issuetype"`
	Worklog              FieldsWorklog `json:"worklog"`
	Timetracking         Timetracking  `json:"timetracking"`
	SP1                  *float64      `json:"customfield_10080"`
	SP2                  *float64      `json:"customfield_10024"`
	SP3                  *float64      `json:"customfield_10101"`
	SPEstimate           *float64      `json:"customfield_10016"`
	SPQAPoint            *float64      `json:"customfield_10087"`
	Summary              string        `json:"summary"`
	Created              string        `json:"created"`
	Updated              string        `json:"updated"`
	Description          *Description  `json:"description,omitempty"`
	Status               Status        `json:"status"`
	Priority             Priority      `json:"priority"`
	Creator              Author        `json:"creator"`
	PEAssigned           Author        `json:"customfield_10033"`
	CustomAssignee       []Author      `json:"customfield_10393"`
	QAAssignee           Author        `json:"customfield_10090"`
	PriorityFeature      CustomField   `json:"customfield_10151"`
	Squad                CustomField   `json:"customfield_10155"`
	EnvironmentBug       CustomField   `json:"customfield_10132"`
	TestCaseType         CustomField   `json:"customfield_10194"`
	AccidentBug          CustomField   `json:"customfield_10156"`
	SeverityBug          CustomField   `json:"customfield_10150"`
	TotalTestCaseMedium  float64       `json:"customfield_10169"`
	TotalTestCaseHigh    float64       `json:"customfield_10170"`
	ClassificationIssue  CustomField   `json:"customfield_10193"`
	IssueTriggerFlow     CustomField   `json:"customfield_10327"`
	TriggerActionIssue   CustomField   `json:"customfield_10328"`
	ImpactIssue          CustomField   `json:"customfield_10329"`
	IssueFromRole        CustomField   `json:"customfield_10332"`
	Parent               *Issue        `json:"parent"`
	Assignee             Author        `json:"assignee"`
	Reporter             Author        `json:"reporter"`
	Labels               []string      `json:"labels"`
	ExpectedStartDate    string        `json:"customfield_10360"`
	ExpectedEndDate      string        `json:"customfield_10361"`
	BugSegmentation      CustomField   `json:"customfield_10459"`
	EngExpectedStartDate string        `json:"customfield_10195"`
	EngExpectedEndDate   string        `json:"customfield_10196"`
	AdditionalTask       CustomField   `json:"customfield_10492"`
	IssueLinks           []IssueLink   `json:"issuelinks"`
	FunctionBug          CustomField   `json:"customfield_10525"`
	DevAssignee          Author        `json:"customfield_10165"`
	FixVersion           []FixVersion  `json:"fixVersions"`
}

type FixVersion struct {
	Self        string `json:"self"`
	ID          string `json:"id"`
	Description string `json:"description"`
	Name        string `json:"name"`
	Archived    bool   `json:"archived"`
	Released    bool   `json:"released"`
}

type IssueLink struct {
	ID      string          `json:"id"`
	Type    IssueLinkType   `json:"type"`
	Inward  *LinkedIssueRef `json:"inwardIssue,omitempty"`
	Outward *LinkedIssueRef `json:"outwardIssue,omitempty"`
}

type IssueLinkType struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Inward  string `json:"inward"`
	Outward string `json:"outward"`
}

type LinkedIssueRef struct {
	ID     string `json:"id"`
	Key    string `json:"key"`
	Self   string `json:"self"`
	Fields Fields `json:"fields,omitempty"`
}

func (f Fields) Parentx() Issue {
	if f.Parent != nil {
		return *f.Parent
	}
	return Issue{}
}
func (i Issue) TicketLink() string {
	return fmt.Sprintf("https://lionparcel.atlassian.net/browse/%s", i.Key)
}

func (f Fields) SP() float64 {
	if f.SP1 != nil {
		return *f.SP1
	}
	if f.SP2 != nil {
		return *f.SP2
	}
	if f.SP3 != nil {
		return *f.SP3
	}
	if f.SPEstimate != nil {
		return *f.SPEstimate
	}
	if f.SPQAPoint != nil {
		return *f.SPQAPoint
	}
	return 0
}
func (f Fields) PEName() string {
	if f.PEAssigned.DisplayName != "" {
		return f.PEAssigned.DisplayName
	}
	for _, assignee := range f.CustomAssignee {
		if assignee.DisplayName != "" {
			return assignee.DisplayName
		}
	}
	return f.Reporter.DisplayName
}
func (f Fields) PEUserID() string {
	if f.PEAssigned.AccountID != "" {
		return f.PEAssigned.AccountID
	}
	for _, assignee := range f.CustomAssignee {
		if assignee.AccountID != "" {
			return assignee.AccountID
		}
	}
	return f.Reporter.AccountID
}

type Issuetype struct {
	Self           string  `json:"self"`
	ID             string  `json:"id"`
	Description    string  `json:"description"`
	IconURL        string  `json:"iconUrl"`
	Name           string  `json:"name"`
	Subtask        bool    `json:"subtask"`
	AvatarID       *int64  `json:"avatarId,omitempty"`
	EntityID       *string `json:"entityId,omitempty"`
	HierarchyLevel int64   `json:"hierarchyLevel"`
}

type Timetracking struct {
	RemainingEstimate        string  `json:"remainingEstimate"`
	TimeSpent                string  `json:"timeSpent"`
	RemainingEstimateSeconds int64   `json:"remainingEstimateSeconds"`
	TimeSpentSeconds         int64   `json:"timeSpentSeconds"`
	OriginalEstimate         *string `json:"originalEstimate,omitempty"`
	OriginalEstimateSeconds  *int64  `json:"originalEstimateSeconds,omitempty"`
}

type FieldsWorklog struct {
	StartAt    int64            `json:"startAt"`
	MaxResults int64            `json:"maxResults"`
	Total      int64            `json:"total"`
	Worklogs   []WorklogElement `json:"worklogs"`
}

type Changelog struct {
	StartAt    int64     `json:"startAt"`
	MaxResults int64     `json:"maxResults"`
	Total      int64     `json:"total"`
	Histories  []History `json:"histories"`
}

type History struct {
	ID      string `json:"id"`
	Author  Author `json:"author"`
	Created string `json:"created"`
	Items   []Item `json:"items"`
}

type Item struct {
	Field      string  `json:"field"`
	Fieldtype  string  `json:"fieldtype"`
	FieldID    string  `json:"fieldId"`
	FromString *string `json:"fromString"`
	ToString   *string `json:"toString"`
}

func (f FieldsWorklog) LastActivityAt(userID string, endDate time.Time) string {
	if len(f.Worklogs) == 0 {
		return ""
	}
	latest := ""
	for _, w := range f.Worklogs {
		if w.Author.AccountID != userID && w.Author.AccountID != "" && userID != "" {
			continue
		}
		if w.Started > latest {
			latest = w.Started
		}
	}
	return latest
}

// FirstAndLastCreatedWorklog returns the first and last created worklog based on the Created field.
func (f FieldsWorklog) FirstAndLastCreatedWorklog() (first, last string) {
	if len(f.Worklogs) == 0 {
		return "", ""
	}
	firstIdx, lastIdx := 0, 0
	for i, w := range f.Worklogs {
		if w.Started < f.Worklogs[firstIdx].Started {
			firstIdx = i
		}
		if w.Started > f.Worklogs[lastIdx].Started {
			lastIdx = i
		}
	}
	return f.Worklogs[firstIdx].Started, f.Worklogs[lastIdx].Started
}

func (f FieldsWorklog) GroomingDate(userID string) string {
	if len(f.Worklogs) == 0 {
		return ""
	}
	groomingDateList := []int64{}
	for _, worklog := range f.Worklogs {
		if worklog.Author.AccountID != userID && worklog.Author.AccountID != "" && userID != "" {
			continue
		}
		comment := worklog.FormattedComment()
		if !strings.Contains(strings.ToLower(comment), "grooming") {
			continue
		}
		worklogDate, err := time.Parse(timex.JiraDateTimeLayout, worklog.Started)
		if err != nil {
			continue
		}
		groomingDateList = append(groomingDateList, worklogDate.Unix())
	}
	slices.Sort(groomingDateList)
	if len(groomingDateList) == 0 {
		return ""
	}
	return time.Unix(groomingDateList[0], 0).Format(timex.JiraDateTimeLayout)
}
func (f FieldsWorklog) DoneBreakDownDate(userID string) string {
	if len(f.Worklogs) == 0 {
		return ""
	}
	for _, worklog := range f.Worklogs {
		if worklog.Author.AccountID != userID && worklog.Author.AccountID != "" && userID != "" {
			continue
		}
		comment := worklog.FormattedComment()
		if !strings.Contains(strings.ToLower(comment), "done") {
			continue
		}
		return worklog.Started
	}
	return ""
}
func (f FieldsWorklog) BreakDownDurationSecond(userID string, endDate time.Time) int64 {
	if len(f.Worklogs) == 0 {
		return 0
	}
	timeSpentSecond := int64(0)
	for _, worklog := range f.Worklogs {
		if (worklog.Author.AccountID != userID && worklog.Author.AccountID != "" && userID != "") || worklog.StartedTime().After(endDate) {
			continue
		}
		comment := worklog.FormattedComment()
		if strings.Contains(strings.ToLower(comment), "[grooming") {
			continue
		}
		timeSpentSecond += worklog.TimeSpentSeconds
	}
	return timeSpentSecond
}
func (f FieldsWorklog) DetailingDurationSecond(userID string, endDate time.Time) int64 {
	if len(f.Worklogs) == 0 {
		return 0
	}
	timeSpentSecond := int64(0)
	for _, worklog := range f.Worklogs {
		if (worklog.Author.AccountID != userID && worklog.Author.AccountID != "" && userID != "") || worklog.StartedTime().After(endDate) {
			continue
		}
		comment := worklog.FormattedComment()
		if strings.Contains(strings.ToLower(comment), "[pairing") {
			continue
		}
		timeSpentSecond += worklog.TimeSpentSeconds
	}
	return timeSpentSecond
}
func (f FieldsWorklog) OperationHourSecond(userID string, endDate time.Time) int64 {
	if len(f.Worklogs) == 0 {
		return 0
	}
	timeSpentSecond := int64(0)
	for _, worklog := range f.Worklogs {
		if (worklog.Author.AccountID != userID && worklog.Author.AccountID != "" && userID != "") || worklog.StartedTime().After(endDate) {
			continue
		}
		comment := worklog.FormattedComment()
		if !strings.Contains(strings.ToLower(comment), "[pairing") {
			continue
		}
		timeSpentSecond += worklog.TimeSpentSeconds
	}
	return timeSpentSecond
}
func (f FieldsWorklog) BreakDownStatus(userID string) BreakDownStatus {
	if len(f.Worklogs) == 0 {
		return ""
	}
	logList := []string{}
	for _, worklog := range f.Worklogs {
		if worklog.Author.AccountID != userID && worklog.Author.AccountID != "" && userID != "" {
			continue
		}
		comment := worklog.FormattedComment()
		if strings.Contains(strings.ToLower(comment), "done") {
			return BreakDownStatusDone
		}
		if strings.Contains(strings.ToLower(comment), "grooming") {
			continue
		}
		logList = append(logList, comment)
	}
	if len(logList) != 0 {
		return BreakDownStatusInProgress
	}
	return BreakDownStatusBacklog
}
func (f FieldsWorklog) IssueStatus(userID string, endDate time.Time) IssueStatus {
	if len(f.Worklogs) == 0 {
		return IssueStatusBacklog
	}
	logList := []string{}
	for _, worklog := range f.Worklogs {
		if (worklog.Author.AccountID != userID && worklog.Author.AccountID != "" && userID != "") || worklog.StartedTime().After(endDate) {
			continue
		}
		comment := worklog.FormattedComment()
		if strings.Contains(strings.ToLower(comment), "done") {
			return IssueStatusDone
		}
		logList = append(logList, comment)
	}
	if len(logList) != 0 {
		return IssueStatusInProgress
	}
	return IssueStatusBacklog
}

type WorklogElement struct {
	Self             string   `json:"self"`
	Author           Author   `json:"author"`
	UpdateAuthor     Author   `json:"updateAuthor"`
	Comment          *Comment `json:"comment,omitempty"`
	Created          string   `json:"created"`
	Updated          string   `json:"updated"`
	Started          string   `json:"started"`
	TimeSpent        string   `json:"timeSpent"`
	TimeSpentSeconds int64    `json:"timeSpentSeconds"`
	ID               string   `json:"id"`
	IssueID          string   `json:"issueId"`
}

func (w WorklogElement) FormattedComment() string {
	if w.Comment == nil {
		return ""
	}
	comment := []string{}
	for _, c := range w.Comment.Content {
		if c.Type != "paragraph" {
			continue
		}
		for _, c := range c.Content {
			if c.Type != "text" {
				continue
			}
			if c.Text == nil {
				continue
			}
			comment = append(comment, *c.Text)
		}
	}
	return strings.Join(comment, "\n")
}
func (w WorklogElement) StartedTime() time.Time {
	startedTime, err := time.Parse(timex.JiraDateTimeLayout, w.Started)
	if err != nil {
		return time.Time{}
	}
	return startedTime
}

type Author struct {
	Self         string     `json:"self"`
	AccountID    string     `json:"accountId"`
	AvatarUrls   AvatarUrls `json:"avatarUrls"`
	DisplayName  string     `json:"displayName"`
	Active       bool       `json:"active"`
	TimeZone     string     `json:"timeZone"`
	AccountType  string     `json:"accountType"`
	EmailAddress *string    `json:"emailAddress,omitempty"`
}

type AvatarUrls struct {
	The48X48 string `json:"48x48"`
	The24X24 string `json:"24x24"`
	The16X16 string `json:"16x16"`
	The32X32 string `json:"32x32"`
}

type Comment struct {
	Type    string           `json:"type"`
	Version int64            `json:"version"`
	Content []CommentContent `json:"content"`
}

type CommentContent struct {
	Type    string           `json:"type"`
	Content []ContentContent `json:"content"`
}

type ContentContent struct {
	Type string  `json:"type"`
	Text *string `json:"text,omitempty"`
}
type CustomField struct {
	Self  string `json:"self"`
	Value string `json:"value"`
	ID    string `json:"id"`
}

type Status struct {
	Self           string         `json:"self"`
	Description    string         `json:"description"`
	IconURL        string         `json:"iconUrl"`
	Name           string         `json:"name"`
	ID             string         `json:"id"`
	StatusCategory StatusCategory `json:"statusCategory"`
}

type StatusCategory struct {
	Self      string `json:"self"`
	ID        int64  `json:"id"`
	Key       string `json:"key"`
	ColorName string `json:"colorName"`
	Name      string `json:"name"`
}

type Priority struct {
	Self    string `json:"self"`
	IconURL string `json:"iconUrl"`
	Name    string `json:"name"`
	ID      string `json:"id"`
}

type Description struct {
	Type    string               `json:"type"`
	Version int64                `json:"version"`
	Content []DescriptionContent `json:"content"`
}

type DescriptionContent struct {
	Type    string                      `json:"type"`
	Content []DescriptionContentElement `json:"content,omitempty"`
	Text    *string                     `json:"text,omitempty"`
}

type DescriptionContentElement struct {
	Type string  `json:"type"`
	Text *string `json:"text,omitempty"`
}

type SprintResponse struct {
	Values []Sprint `json:"values"`
}

type Sprint struct {
	ID           int    `json:"id"`
	Name         string `json:"name"`
	State        string `json:"state"`
	StartDate    string `json:"startDate,omitempty"`
	EndDate      string `json:"endDate,omitempty"`
	CompleteDate string `json:"completeDate,omitempty"`
	BoardID      int    `json:"originBoardId"`
	Goal         string `json:"goal,omitempty"`
}

type ChangelogResponse struct {
	Expand    string `json:"expand"`
	ID        string `json:"id"`
	Self      string `json:"self"`
	Key       string `json:"key"`
	Changelog struct {
		StartAt    int `json:"startAt"`
		MaxResults int `json:"maxResults"`
		Total      int `json:"total"`
		Histories  []struct {
			ID     string `json:"id"`
			Author struct {
				Self         string `json:"self"`
				AccountID    string `json:"accountId"`
				EmailAddress string `json:"emailAddress"`
				AvatarUrls   struct {
					Size48 string `json:"48x48"`
					Size24 string `json:"24x24"`
					Size16 string `json:"16x16"`
					Size32 string `json:"32x32"`
				} `json:"avatarUrls"`
				DisplayName string `json:"displayName"`
				Active      bool   `json:"active"`
				TimeZone    string `json:"timeZone"`
				AccountType string `json:"accountType"`
			} `json:"author"`
			Created string `json:"created"`
			Items   []struct {
				Field      string  `json:"field"`
				Fieldtype  string  `json:"fieldtype"`
				FieldID    string  `json:"fieldId"`
				From       *string `json:"from"`
				FromString *string `json:"fromString"`
				To         *string `json:"to"`
				ToString   *string `json:"toString"`
			} `json:"items"`
		} `json:"histories"`
	} `json:"changelog"`
}

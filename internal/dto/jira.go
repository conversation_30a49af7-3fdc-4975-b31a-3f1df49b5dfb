package dto

import "github.com/Lionparcel/pentools/internal/model"

type GetJiraReportRequest struct {
	StartDate string `query:"start_date" example:"2023-01-01"`
	EndDate   string `query:"end_date" example:"2023-02-01"`
	JQL       string `query:"jql" example:"worklogAuthor IN (712020:b6ff5117-0224-4899-a5fc-7499e7a23188)"`
	Format    string `query:"format" example:"csv" default:"json"`
}
type GetJiraReportResponse struct {
	Issues []JiraIssue `json:"issues"`
}
type JiraIssue struct {
	Name                  string  `json:"name"`
	UserID                string  `json:"user_id"`
	Type                  string  `json:"type"`
	IssueName             string  `json:"issue_name"`
	LinkTicket            string  `json:"link_ticket"`
	SP                    float64 `json:"sp"`
	TicketCreatedAt       string  `json:"ticket_created_at"`
	WorkLogDate           string  `json:"work_log_date"`
	WorkLogTimeSpentHour  float64 `json:"work_log_time_spent_hour"`
	WorkLogTimeSpentHuman string  `json:"work_log_time_spent_human"`
	WorkLogComment        string  `json:"work_log_comment"`
}

type GetEpicsRequest struct {
	StartDate string   `query:"start_date" example:"2023-01-01" json:"start_date"`
	EndDate   string   `query:"end_date" example:"2023-02-01" json:"end_date"`
	UserIDs   []string `query:"user_ids" json:"user_ids"`
}
type GetUserEpicRequest struct {
	StartDate string   `query:"start_date"  example:"2023-01-01"`
	EndDate   string   `query:"end_date"  example:"2023-02-01"`
	UserID    string   `param:"user_id" example:"712020:b6ff5117-0224-4899-a5fc-7499e7a23188"`
	UserIDs   []string `query:"user_ids"`
}
type GetUserEpicResponse struct {
	Epics []EpicOfUser `json:"epics"`
}
type EpicOfUser struct {
	Reporter                string                `json:"reporter"`
	EpicName                string                `json:"epic_name"`
	EpicLink                string                `json:"epic_link"`
	GroomingDate            string                `json:"grooming_date"`
	DoneBreakDownDate       string                `json:"done_break_down_date"`
	SP                      float64               `json:"sp"`
	BreakDownDurationHour   float64               `json:"break_down_duration_hour"`
	BreakDownDurationSecond int64                 `json:"break_down_duration_second"`
	BreakDownDurationHuman  string                `json:"break_down_duration_human"`
	BreakDownStatus         model.BreakDownStatus `json:"break_down_status"`
	OperationHour           float64               `json:"operation_hour"`
	OperationHourSecond     int64                 `json:"operation_hour_second"`
	OperationHourHuman      string                `json:"operation_hour_human"`
	DetailingDurationHour   float64               `json:"detailing_duration_hour"`
	DetailingDurationSecond int64                 `json:"detailing_duration_second"`
	DetailingDurationHuman  string                `json:"detailing_duration_human"`
	ExpectedStartDate       string                `json:"expected_start_date"`
	ExpectedEndDate         string                `json:"expected_end_date"`
	Issues                  []EpicOfUserIssue     `json:"issues"`
	Worklogs                []Worklog             `json:"worklogs"`
}
type EpicOfUserIssue struct {
	Name                         string            `json:"name"`
	UserID                       string            `json:"user_id"`
	Type                         string            `json:"type"`
	IssueName                    string            `json:"issue_name"`
	LinkTicket                   string            `json:"link_ticket"`
	Status                       model.IssueStatus `json:"status"`
	TimeSpentHour                float64           `json:"time_spent_hour"`
	TimeSpentHourSecond          int64             `json:"time_spent_hour_second"`
	TimeSpentHuman               string            `json:"time_spent_human"`
	TimeSpentDetailingHour       float64           `json:"time_spent_detailing_hour"`
	TimeSpentDetailingHourSecond int64             `json:"time_spent_detailing_hour_second"`
	TimeSpentDetailingHuman      string            `json:"time_spent_detailing_human"`
	TimeSpentOperationHour       float64           `json:"time_spent_operation_hour"`
	TimeSpentOperationHourSecond int64             `json:"time_spent_operation_hour_second"`
	TimeSpentOperationHuman      string            `json:"time_spent_operation_human"`
	SP                           float64           `json:"sp"`
	LastActivityAt               string            `json:"last_activity_at"`
	ExpectedStartDate            string            `json:"expected_start_date"`
	ExpectedEndDate              string            `json:"expected_end_date"`
	Worklogs                     []Worklog         `json:"worklogs"`
}

type GetBugsRequest struct {
	StartDate string `query:"start_date" example:"2023-01-01"`
	EndDate   string `query:"end_date" example:"2023-02-01"`
	PEUserID  string `query:"pe_user_id" example:"712020:b6ff5117-0224-4899-a5fc-7499e7a23188"`
}
type GetJiraBugsResponse struct {
	Bugs []JiraBug `json:"bugs"`
}
type JiraBug struct {
	BugName             string   `json:"bug_name"`               // summary
	BugLink             string   `json:"bug_link"`               // Key
	QAAssignee          string   `json:"qa_assignee"`            // customfield_10090.displayName
	PriorityFeature     string   `json:"priority_feature"`       // customfield_10151.value
	Squad               string   `json:"squad"`                  // customfield_10155.value
	EnvironmentBug      string   `json:"environment_bug"`        // customfield_10132.value
	TestCaseType        string   `json:"test_case_type"`         // customfield_10194.value
	AccidentBug         string   `json:"accident_bug"`           // customfield_10156.value
	SeverityBug         string   `json:"severity_bug"`           // customfield_10150.value
	TotalTestCaseMedium float64  `json:"total_test_case_medium"` // customfield_10169
	TotalTestCaseHigh   float64  `json:"total_test_case_high"`   // customfield_10170
	ClassificationIssue string   `json:"classification_issue"`   // customfield_10193.value
	IssueTriggerFlow    string   `json:"issue_trigger_flow"`     // customfield_10327.value
	TriggerActionIssue  string   `json:"trigger_action_issue"`   // customfield_10328.value
	ImpactIssue         string   `json:"impact_issue"`           // customfield_10329.value
	IssueFromRole       string   `json:"issue_from_role"`        // customfield_10332.value
	Reporter            string   `json:"reporter"`               // reporter.displayName
	Assignee            string   `json:"assignee"`               // assignee.displayName
	ParentName          string   `json:"parent_name"`            // parent.key
	ParentLink          string   `json:"parent_link"`            // parent.key
	TagRelease          string   `json:"tag_release"`
	PEName              string   `json:"pe_name"`           // .displayName
	PEUserID            string   `json:"pe_user_id"`        // customfield_10157.value
	TicketCreatedAt     string   `json:"ticket_created_at"` // created
	Labels              []string `json:"labels"`            // labels
	BugSegmentation     string   `json:"bug_segmentation"`  // customfield_10459.value
}

type GetTasksRequest struct {
	JQL string `query:"jql" example:"project = 'PROJ' AND issuetype = Task"`
}

type GetCountRequest struct {
	JQL string `query:"jql" example:"project = 'PROJ' AND issuetype = Task"`
}

type GetCountResponse struct {
	Count int `json:"count"`
}

type GetTasksResponse struct {
	Tasks []JiraTask `json:"tasks"`
}
type JiraTask struct {
	TaskName        string    `json:"task_name"`         // summary
	TaskLink        string    `json:"task_link"`         // key
	Type            string    `json:"type"`              // issuetype.name
	Status          string    `json:"status"`            // status.name
	Priority        string    `json:"priority"`          // priority.name
	Assignee        string    `json:"assignee"`          // assignee.displayName
	Reporter        string    `json:"reporter"`          // reporter.displayName
	Created         string    `json:"created"`           // created
	Updated         string    `json:"updated"`           // updated
	SP              float64   `json:"sp"`                // story points
	Labels          []string  `json:"labels"`            // labels
	ParentName      string    `json:"parent_name"`       // parent.fields.summary
	ParentLink      string    `json:"parent_link"`       // parent key
	TimeSpentHours  float64   `json:"time_spent_hours"`  // time spent in hours
	TimeSpentSecond int64     `json:"time_spent_second"` // time spent in second
	TimeSpentHuman  string    `json:"time_spent_human"`  // human readable time spent
	Worklogs        []Worklog `json:"worklogs"`          // worklogs
}

type GetSprintRequest struct {
	BoardID   int    `query:"board_id" example:"75"`
	StartDate string `query:"start_date" example:"2023-07-03"`
	EndDate   string `query:"end_date" example:"2023-07-16"`
}

type GetSprintResponse struct {
	Sprints []JiraSprint `json:"values"`
}

type JiraSprint struct {
	ID            int    `json:"id"`
	Name          string `json:"name"`
	State         string `json:"state"`
	StartDate     string `json:"startDate,omitempty"`
	EndDate       string `json:"endDate,omitempty"`
	CompleteDate  string `json:"completeDate,omitempty"`
	BoardID       int    `json:"originBoardId"`
	Goal          string `json:"goal,omitempty"`
}

type GetChangelogRequest struct {
	IssueKey string `param:"issue_key" example:"GQA-1234"`
}

type GetChangelogResponse struct {
	Expand    string    `json:"expand"`
	ID        string    `json:"id"`
	Self      string    `json:"self"`
	Key       string    `json:"key"`
	Changelog Changelog `json:"changelog"`
}

type Changelog struct {
	StartAt    int                `json:"startAt"`
	MaxResults int                `json:"maxResults"`
	Total      int                `json:"total"`
	Histories  []ChangelogHistory `json:"histories"`
}

type ChangelogHistory struct {
	ID      string            `json:"id"`
	Author  ChangelogAuthor   `json:"author"`
	Created string            `json:"created"`
	Items   []ChangelogItem   `json:"items"`
}

type ChangelogAuthor struct {
	AccountID    string `json:"accountId"`
	DisplayName  string `json:"displayName"`
	EmailAddress string `json:"emailAddress"`
}

type ChangelogItem struct {
	Field      string  `json:"field"`
	Fieldtype  string  `json:"fieldtype"`
	FieldID    string  `json:"fieldId"`
	From       *string `json:"from"`
	FromString *string `json:"fromString"`
	To         *string `json:"to"`
	ToString   *string `json:"toString"`
}